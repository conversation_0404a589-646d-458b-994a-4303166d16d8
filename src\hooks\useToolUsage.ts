import { useCallback } from 'react';
import { useUserPreferencesStore } from '@/store/userPreferencesStore';
import { TOOLS } from '@/lib/constants/tools';

export const useToolUsage = () => {
  const { recordToolUsage, addToRecent } = useUserPreferencesStore();

  const trackToolUsage = useCallback((toolId: string, action: string, metadata?: Record<string, unknown>) => {
    const tool = TOOLS.find(t => t.id === toolId);
    if (!tool) {
      console.warn(`Tool with id ${toolId} not found`);
      return;
    }

    recordToolUsage({
      toolId,
      toolName: tool.name,
      action,
      metadata,
    });
  }, [recordToolUsage]);

  const trackToolAccess = useCallback((toolId: string) => {
    trackToolUsage(toolId, '访问工具');
    addToRecent(toolId);
  }, [trackToolUsage, addToRecent]);

  const trackToolAction = useCallback((toolId: string, action: string, details?: string) => {
    const actionText = details ? `${action}: ${details}` : action;
    trackToolUsage(toolId, actionText);
  }, [trackToolUsage]);

  return {
    trackToolUsage,
    trackToolAccess,
    trackToolAction,
  };
};

// 常用的工具操作类型
export const TOOL_ACTIONS = {
  ACCESS: '访问工具',
  CONVERT: '转换',
  FORMAT: '格式化',
  GENERATE: '生成',
  COMPRESS: '压缩',
  ENCODE: '编码',
  DECODE: '解码',
  COPY: '复制',
  DOWNLOAD: '下载',
  UPLOAD: '上传',
  CLEAR: '清空',
  RESET: '重置',
} as const;
