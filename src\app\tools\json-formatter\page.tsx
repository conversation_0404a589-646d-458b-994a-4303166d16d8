import { Metadata } from 'next';
import JsonFormatterClient from './JsonFormatterClient';

export const metadata: Metadata = {
  title: 'JSON格式化工具 - 在线JSON美化验证器 | Tool List',
  description: '专业的JSON格式化工具，支持JSON美化、压缩、验证，语法高亮显示，错误检测。程序员开发调试的最佳JSON处理工具，支持大文件处理和格式转换。',
  keywords: [
    'JSON格式化', 'JSON美化', 'JSON验证', 'JSON压缩', 'JSON工具',
    'JSON解析', '语法高亮', '错误检测', '在线工具', 'JSON formatter',
    '开发者工具', 'API调试', 'JSON编辑器', '前端工具'
  ],
  openGraph: {
    title: 'JSON格式化工具 - 免费在线JSON处理器',
    description: '支持JSON美化、压缩、验证，语法高亮，开发者必备工具',
    url: 'https://cypress.fun/tools/json-formatter',
    siteName: 'Tool List',
    images: [
      {
        url: 'https://cypress.fun/og-json-formatter.png',
        width: 1200,
        height: 630,
        alt: 'JSON格式化工具',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'JSON格式化工具 - Tool List',
    description: '专业的JSON格式化和验证工具，支持语法高亮和错误检测',
    images: ['https://cypress.fun/og-json-formatter.png'],
  },
  alternates: {
    canonical: 'https://cypress.fun/tools/json-formatter',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function JsonFormatterPage() {
  return <JsonFormatterClient />;
}
