import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db/mongodb';
import { Feedback } from '@/lib/db/models';
import { z } from 'zod';

// 查询参数验证schema
const querySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 50)),
  status: z.enum(['pending', 'in-progress', 'resolved', 'closed']).optional(),
  type: z.enum(['bug', 'feature', 'improvement', 'question', 'other']).optional(),
});

// 获取用户反馈列表
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);
    
    // 检查用户登录状态
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: '请先登录',
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryResult = querySchema.safeParse(Object.fromEntries(searchParams));
    
    if (!queryResult.success) {
      return NextResponse.json({
        success: false,
        message: '查询参数验证失败',
        errors: queryResult.error.errors,
      }, { status: 400 });
    }

    const { page, limit, status, type } = queryResult.data;

    // 构建查询条件
    const query: Record<string, unknown> = {
      userId: session.user.id,
    };

    if (status) {
      query.status = status;
    }

    if (type) {
      query.type = type;
    }

    // 获取总数
    const total = await Feedback.countDocuments(query);

    // 获取反馈列表
    const feedbacks = await Feedback.find(query)
      .populate('resolvedBy', 'username email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip((page - 1) * limit)
      .lean();

    // 统计数据
    const stats = await Feedback.aggregate([
      { $match: { userId: session.user.id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    const statusStats = stats.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        feedbacks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats: statusStats,
      },
    });

  } catch (error) {
    console.error('获取用户反馈列表失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取反馈列表失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
