import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui';

export default function ContactPage() {
  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <span className="text-gray-900">联系我们</span>
        </nav>

        <h1 className="text-3xl font-bold text-gray-900">联系我们</h1>
      </div>

      {/* 联系内容 */}
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">联系方式</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">📧</span>
                <div>
                  <h3 className="font-medium text-gray-900">邮箱</h3>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
              
              {/* <div className="flex items-center space-x-3">
                <span className="text-2xl">📱</span>
                <div>
                  <h3 className="font-medium text-gray-900">GitHub</h3>
                  <a 
                    href="https://github.com/butterfly4147/toollist" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    github.com/butterfly4147/toollist
                  </a>
                </div>
              </div> */}

              <div className="flex items-center space-x-3">
                <span className="text-2xl">🌐</span>
                <div>
                  <h3 className="font-medium text-gray-900">网站</h3>
                  <p className="text-gray-600">cypress.fun</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">反馈与建议</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              我们非常重视用户的反馈和建议。如果您在使用过程中遇到任何问题，或者有新的工具需求，
              请通过以下方式联系我们：
            </p>
            <ul className="space-y-2 text-gray-600">
              {/* <li>• 通过 GitHub Issues 提交问题或建议</li> */}
              <li>• 发送邮件到 <EMAIL></li>
              {/* <li>• 提交 Pull Request 贡献代码</li> */}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">常见问题</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 工具是否免费使用？</h3>
                <p className="text-gray-600">A: 是的，所有工具都是完全免费的，无需注册即可使用。</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 数据是否安全？</h3>
                <p className="text-gray-600">A: 所有处理都在客户端进行，我们不会存储您的任何数据。</p>
              </div>
              
              {/* <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 如何贡献新工具？</h3>
                <p className="text-gray-600">A: 您可以通过 GitHub 提交 Pull Request 来贡献新的工具。</p>
              </div> */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 返回首页 */}
      <div className="mt-8 text-center">
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  );
}
