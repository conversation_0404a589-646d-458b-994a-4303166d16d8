import { Metadata } from 'next';
import Base64ConverterClient from './Base64ConverterClient';

export const metadata: Metadata = {
  title: 'Base64编码解码 - 在线Base64转换工具 | Tool List',
  description: '在线Base64编码解码工具，支持文本、中文、URL的Base64转换，双向转换，即时处理。开发者编码解码的首选工具，支持大文件处理和批量转换。',
  keywords: [
    'Base64编码', 'Base64解码', 'Base64转换', '编码工具', '解码工具',
    '在线编码', '文本编码', '中文编码', 'URL编码', 'Base64 converter',
    '开发者工具', '数据转换', '字符串编码', '前端工具'
  ],
  openGraph: {
    title: 'Base64编码解码 - 免费在线转换工具',
    description: '支持文本、中文、URL的Base64双向转换，开发者必备工具',
    url: 'https://cypress.fun/tools/base64-converter',
    siteName: 'Tool List',
    images: [
      {
        url: 'https://cypress.fun/og-base64.png',
        width: 1200,
        height: 630,
        alt: 'Base64编码解码工具',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Base64编码解码 - Tool List',
    description: '在线Base64编码解码工具，支持中文和特殊字符',
    images: ['https://cypress.fun/og-base64.png'],
  },
  alternates: {
    canonical: 'https://cypress.fun/tools/base64-converter',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function Base64ConverterPage() {
  return <Base64ConverterClient />;
}
