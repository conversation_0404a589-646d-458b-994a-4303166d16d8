import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Tool } from '@/lib/db/models';
import { TOOLS } from '@/lib/constants/tools';

export async function POST(request: NextRequest) {
  try {
    // 连接数据库
    await connectDB();

    // 获取请求参数
    const body = await request.json();
    const { action = 'sync', force = false } = body;

    if (action === 'sync') {
      // 同步工具数据到数据库
      const results = {
        created: 0,
        updated: 0,
        skipped: 0,
        errors: [] as Array<{ tool: string; error: string }>,
      };

      for (const toolData of TOOLS) {
        try {
          // 检查工具是否已存在
          const existingTool = await Tool.findOne({ 
            $or: [
              { path: toolData.path },
              { name: toolData.name }
            ]
          });

          if (existingTool && !force) {
            // 如果工具已存在且不强制更新，跳过
            results.skipped++;
            continue;
          }

          if (existingTool && force) {
            // 更新现有工具
            await Tool.findByIdAndUpdate(existingTool._id, {
              name: toolData.name,
              description: toolData.description,
              icon: toolData.icon,
              category: toolData.category,
              tags: toolData.tags,
              path: toolData.path,
              isPublic: toolData.isPublic,
              requiredAuth: toolData.requiredAuth,
              config: toolData.config,
              updatedAt: new Date(),
            });
            results.updated++;
          } else {
            // 创建新工具
            await Tool.create({
              name: toolData.name,
              description: toolData.description,
              icon: toolData.icon,
              category: toolData.category,
              tags: toolData.tags,
              path: toolData.path,
              isPublic: toolData.isPublic,
              requiredAuth: toolData.requiredAuth,
              visitCount: toolData.visitCount || 0,
              addedBy: 'system', // 系统添加的工具
              config: toolData.config,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
            results.created++;
          }
        } catch (error) {
          console.error(`同步工具 ${toolData.name} 失败:`, error);
          results.errors.push({
            tool: toolData.name,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: '工具数据同步完成',
        data: {
          total: TOOLS.length,
          ...results,
        },
      });
    }

    if (action === 'count') {
      // 获取数据库中的工具数量
      const dbCount = await Tool.countDocuments();
      const constantsCount = TOOLS.length;

      return NextResponse.json({
        success: true,
        data: {
          database: dbCount,
          constants: constantsCount,
          needSync: dbCount !== constantsCount,
        },
      });
    }

    return NextResponse.json({
      success: false,
      message: '不支持的操作',
      availableActions: ['sync', 'count'],
    }, { status: 400 });

  } catch (error) {
    console.error('工具同步失败:', error);

    return NextResponse.json({
      success: false,
      message: '工具同步失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // 连接数据库
    await connectDB();

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'count';

    if (action === 'count') {
      // 获取数据库中的工具数量
      const dbCount = await Tool.countDocuments();
      const constantsCount = TOOLS.length;

      return NextResponse.json({
        success: true,
        data: {
          database: dbCount,
          constants: constantsCount,
          needSync: dbCount !== constantsCount,
        },
      });
    }

    if (action === 'list') {
      // 获取数据库中的所有工具
      const tools = await Tool.find({}, {
        name: 1,
        category: 1,
        path: 1,
        visitCount: 1,
        createdAt: 1,
        updatedAt: 1,
      }).sort({ category: 1, name: 1 });

      return NextResponse.json({
        success: true,
        data: tools,
      });
    }

    if (action === 'compare') {
      // 比较常量和数据库中的工具
      const dbTools = await Tool.find({}, { name: 1, path: 1, category: 1 });
      const dbToolPaths = new Set(dbTools.map(t => t.path));
      const constantPaths = new Set(TOOLS.map(t => t.path));

      const onlyInConstants = TOOLS.filter(t => !dbToolPaths.has(t.path));
      const onlyInDatabase = dbTools.filter(t => !constantPaths.has(t.path));

      return NextResponse.json({
        success: true,
        data: {
          onlyInConstants,
          onlyInDatabase,
          totalConstants: TOOLS.length,
          totalDatabase: dbTools.length,
        },
      });
    }

    return NextResponse.json({
      success: false,
      message: '不支持的操作',
      availableActions: ['count', 'list', 'compare'],
    }, { status: 400 });

  } catch (error) {
    console.error('获取工具信息失败:', error);

    return NextResponse.json({
      success: false,
      message: '获取工具信息失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
