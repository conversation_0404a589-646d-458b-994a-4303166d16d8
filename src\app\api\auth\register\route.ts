import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';
import { z } from 'zod';

// 注册请求验证 schema
const registerSchema = z.object({
  email: z.string()
    .min(1, '邮箱不能为空')
    .email('请输入有效的邮箱地址')
    .refine(email => {
      console.log(`验证邮箱: ${email}`);
      // 检查邮箱格式是否符合更宽松的标准
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isValidFormat = emailRegex.test(email);
      console.log(`邮箱格式验证: ${isValidFormat ? '通过' : '失败'}`);
      return isValidFormat;
    }, {
      message: '邮箱格式无效',
    }),
  username: z.string()
    .optional()
    .refine((val) => !val || (val.length >= 3 && val.length <= 20), '用户名需要3-20个字符')
    .refine((val) => !val || /^[a-zA-Z0-9_]+$/.test(val), '用户名只能包含字母、数字和下划线'),
  password: z.string()
    .min(8, '密码至少需要8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

export async function POST(request: NextRequest) {
  try {
    // 连接数据库
    await connectDB();

    // 解析请求体
    const body = await request.json();
    
    // 验证输入数据
    console.log('开始验证注册数据:', JSON.stringify(body, null, 2));
    const validationResult = registerSchema.safeParse(body);
    
    if (!validationResult.success) {
      console.log('验证失败:', JSON.stringify(validationResult.error.errors, null, 2));
      return NextResponse.json({
        success: false,
        message: '输入数据验证失败',
        errors: validationResult.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      }, { status: 400 });
    }

    const { email, username, password } = validationResult.data;

    // 检查邮箱是否已存在
    const existingUserByEmail = await User.findOne({ email: email.toLowerCase() });
    if (existingUserByEmail) {
      return NextResponse.json({
        success: false,
        message: '该邮箱已被注册',
        field: 'email',
      }, { status: 409 });
    }

    // 处理用户名：如果为空，使用邮箱前缀作为默认用户名
    let finalUsername = username;
    if (!finalUsername || finalUsername.trim() === '') {
      const emailPrefix = email.split('@')[0];
      finalUsername = emailPrefix;

      // 如果邮箱前缀已被使用，添加随机数字后缀
      let counter = 1;
      let tempUsername = finalUsername;
      while (await User.findOne({ username: tempUsername })) {
        tempUsername = `${finalUsername}${counter}`;
        counter++;
      }
      finalUsername = tempUsername;
    } else {
      // 检查用户名是否已存在
      const existingUserByUsername = await User.findOne({ username: finalUsername });
      if (existingUserByUsername) {
        return NextResponse.json({
          success: false,
          message: '该用户名已被使用',
          field: 'username',
        }, { status: 409 });
      }
    }

    // 创建新用户
    const newUser = new User({
      email: email.toLowerCase(),
      username: finalUsername,
      password,
      role: 'user',
      isEmailVerified: false, // 实际项目中需要邮箱验证
      providers: [{
        provider: 'credentials',
        providerId: email.toLowerCase(),
      }],
    });

    // 保存用户（密码会自动加密）
    await newUser.save();

    // 返回成功响应（不包含敏感信息）
    return NextResponse.json({
      success: true,
      message: '注册成功',
      user: {
        id: newUser._id,
        email: newUser.email,
        username: newUser.username,
        role: newUser.role,
        createdAt: newUser.createdAt,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('注册失败:', error);

    // 处理 MongoDB 重复键错误
    if (error instanceof Error && 'code' in error && error.code === 11000) {
      const mongoError = error as unknown as { keyPattern: Record<string, number> };
      const duplicateField = Object.keys(mongoError.keyPattern)[0];
      const fieldName = duplicateField === 'email' ? '邮箱' : '用户名';
      
      return NextResponse.json({
        success: false,
        message: `该${fieldName}已被使用`,
        field: duplicateField,
      }, { status: 409 });
    }

    return NextResponse.json({
      success: false,
      message: '注册失败，请稍后重试',
      error: process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : '未知错误' : undefined,
    }, { status: 500 });
  }
}

// 检查用户名/邮箱是否可用的 GET 接口
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const username = searchParams.get('username');

    if (!email && !username) {
      return NextResponse.json({
        success: false,
        message: '请提供邮箱或用户名参数',
      }, { status: 400 });
    }

    const checks: { field: string; available: boolean }[] = [];

    if (email) {
      const existingEmail = await User.findOne({ email: email.toLowerCase() });
      checks.push({
        field: 'email',
        available: !existingEmail,
      });
    }

    if (username) {
      const existingUsername = await User.findOne({ username });
      checks.push({
        field: 'username',
        available: !existingUsername,
      });
    }

    return NextResponse.json({
      success: true,
      checks,
    });

  } catch (error) {
    console.error('检查用户信息失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '检查失败，请稍后重试',
    }, { status: 500 });
  }
}
