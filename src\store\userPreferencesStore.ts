import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserPreferences, DEFAULT_USER_PREFERENCES, ToolUsageRecord, UserStats } from '@/types/user';

interface UserPreferencesState {
  // 偏好设置
  preferences: UserPreferences;
  
  // 使用历史
  usageHistory: ToolUsageRecord[];
  
  // 用户统计
  stats: UserStats;
  
  // 加载状态
  isLoading: boolean;
  
  // Actions
  updatePreference: <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => void;
  
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  
  addToFavorites: (toolId: string) => void;
  removeFromFavorites: (toolId: string) => void;
  
  addToRecent: (toolId: string) => void;
  
  recordToolUsage: (record: Omit<ToolUsageRecord, 'id' | 'usedAt'>) => void;
  
  clearHistory: () => void;
  
  resetPreferences: () => void;
  
  // 获取方法
  isFavorite: (toolId: string) => boolean;
  getRecentTools: (limit?: number) => string[];
  getUsageStats: () => UserStats;
}

export const useUserPreferencesStore = create<UserPreferencesState>()(
  persist(
    (set, get) => ({
      preferences: DEFAULT_USER_PREFERENCES,
      usageHistory: [],
      stats: {
        totalToolsUsed: 0,
        totalUsageCount: 0,
        favoriteToolsCount: 0,
        streakDays: 0,
        lastActiveDate: new Date(),
      },
      isLoading: false,

      updatePreference: (key, value) => {
        set((state) => ({
          preferences: {
            ...state.preferences,
            [key]: value,
          },
        }));
      },

      updatePreferences: (newPreferences) => {
        set((state) => ({
          preferences: {
            ...state.preferences,
            ...newPreferences,
          },
        }));
      },

      addToFavorites: (toolId) => {
        set((state) => {
          const favoriteTools = [...state.preferences.favoriteTools];
          if (!favoriteTools.includes(toolId)) {
            favoriteTools.push(toolId);
          }
          return {
            preferences: {
              ...state.preferences,
              favoriteTools,
            },
            stats: {
              ...state.stats,
              favoriteToolsCount: favoriteTools.length,
            },
          };
        });
      },

      removeFromFavorites: (toolId) => {
        set((state) => {
          const favoriteTools = state.preferences.favoriteTools.filter(
            (id) => id !== toolId
          );
          return {
            preferences: {
              ...state.preferences,
              favoriteTools,
            },
            stats: {
              ...state.stats,
              favoriteToolsCount: favoriteTools.length,
            },
          };
        });
      },

      addToRecent: (toolId) => {
        set((state) => {
          const recentTools = [toolId, ...state.preferences.recentTools.filter(id => id !== toolId)];
          // 只保留最近10个
          const limitedRecent = recentTools.slice(0, 10);
          
          return {
            preferences: {
              ...state.preferences,
              recentTools: limitedRecent,
            },
          };
        });
      },

      recordToolUsage: (record) => {
        const newRecord: ToolUsageRecord = {
          ...record,
          id: `usage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          usedAt: new Date(),
        };

        set((state) => {
          const usageHistory = [newRecord, ...state.usageHistory];
          // 只保留最近1000条记录
          const limitedHistory = usageHistory.slice(0, 1000);
          
          // 更新统计信息
          const usedTools = new Set(limitedHistory.map(h => h.toolId));
          const totalUsageCount = limitedHistory.length;
          
          // 计算连续使用天数
          const today = new Date().toDateString();
          const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();
          const lastActiveDate = state.stats.lastActiveDate.toDateString();
          
          let streakDays = state.stats.streakDays;
          if (lastActiveDate === yesterday) {
            streakDays += 1;
          } else if (lastActiveDate !== today) {
            streakDays = 1;
          }

          return {
            usageHistory: limitedHistory,
            stats: {
              totalToolsUsed: usedTools.size,
              totalUsageCount,
              favoriteToolsCount: state.preferences.favoriteTools.length,
              streakDays,
              lastActiveDate: new Date(),
            },
          };
        });

        // 同时添加到最近使用
        get().addToRecent(record.toolId);
      },

      clearHistory: () => {
        set((state) => ({
          usageHistory: [],
          stats: {
            ...state.stats,
            totalUsageCount: 0,
            streakDays: 0,
          },
        }));
      },

      resetPreferences: () => {
        set({
          preferences: DEFAULT_USER_PREFERENCES,
          usageHistory: [],
          stats: {
            totalToolsUsed: 0,
            totalUsageCount: 0,
            favoriteToolsCount: 0,
            streakDays: 0,
            lastActiveDate: new Date(),
          },
        });
      },

      isFavorite: (toolId) => {
        return get().preferences.favoriteTools.includes(toolId);
      },

      getRecentTools: (limit = 5) => {
        return get().preferences.recentTools.slice(0, limit);
      },

      getUsageStats: () => {
        return get().stats;
      },
    }),
    {
      name: 'user-preferences-storage',
      version: 1,
    }
  )
);
