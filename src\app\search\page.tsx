'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import SearchBox from '@/components/search/SearchBox';
import { useSearchStore } from '@/store/searchStore';
import { SEARCH_CATEGORIES } from '@/types/search';

const SearchResultsContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const {
    results,
    filters,
    isSearching,
    search,
    updateFilters,
    resetFilters,
    recordClick,
  } = useSearchStore();

  const searchQuery = searchParams.get('q') || '';

  useEffect(() => {
    if (searchQuery) {
      search(searchQuery);
    }
    setIsLoading(false);
  }, [searchQuery, search]);

  const handleResultClick = (resultId: string, url: string) => {
    recordClick(resultId, searchQuery);
    router.push(url);
  };

  const handleNewSearch = (newQuery: string) => {
    router.push(`/search?q=${encodeURIComponent(newQuery)}`);
  };

  const groupedResults = results.reduce((groups, result) => {
    const type = result.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(result);
    return groups;
  }, {} as Record<string, typeof results>);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 搜索框 */}
        <div className="mb-8">
          <SearchBox
            autoFocus={!searchQuery}
            onSearch={handleNewSearch}
            className="max-w-2xl mx-auto"
            initialValue={searchQuery}
            controlled={true}
            showSuggestions={true}
          />
        </div>

        {/* 搜索信息 */}
        {searchQuery && (
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  搜索结果
                </h1>
                <p className="text-gray-600">
                  关于 &ldquo;{searchQuery}&rdquo; 的搜索结果，共找到 {results.length} 个结果
                </p>
              </div>

              {/* 过滤器 */}
              <div className="flex items-center space-x-4">
                <select
                  value={filters.type}
                  onChange={(e) => updateFilters({ type: e.target.value as 'all' | 'tool' | 'website' | 'page' | 'help' })}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">全部类型</option>
                  {Object.entries(SEARCH_CATEGORIES).map(([key, category]) => (
                    <option key={key} value={key}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>

                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilters({ sortBy: e.target.value as 'relevance' | 'name' | 'category' | 'recent' })}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="relevance">按相关性</option>
                  <option value="name">按名称</option>
                  <option value="category">按分类</option>
                  <option value="recent">按时间</option>
                </select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 搜索结果 */}
        {isSearching ? (
          <div className="text-center py-12">
            <div className="animate-spin text-4xl mb-4">🔍</div>
            <p className="text-gray-600">搜索中...</p>
          </div>
        ) : results.length > 0 ? (
          <div className="space-y-8">
            {Object.entries(groupedResults).map(([type, typeResults]) => {
              const category = SEARCH_CATEGORIES[type as keyof typeof SEARCH_CATEGORIES];
              return (
                <div key={type}>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <span className="mr-2">{category.icon}</span>
                    {category.name} ({typeResults.length})
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {typeResults.map((result) => (
                      <Card
                        key={result.id}
                        className="hover:shadow-lg transition-shadow cursor-pointer"
                        onClick={() => handleResultClick(result.id, result.url)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="text-2xl">{result.icon}</div>
                              <div className="flex-1">
                                <CardTitle className="text-lg">{result.title}</CardTitle>
                                <p className="text-sm text-gray-500 mt-1">
                                  {result.description}
                                </p>
                              </div>
                            </div>
                            <div className="text-xs text-gray-400">
                              {Math.round(result.relevanceScore)}%
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {result.tags?.slice(0, 3).map((tag, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                            <div className="text-xs text-gray-500">
                              {result.category}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        ) : searchQuery ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              没有找到相关结果
            </h3>
            <p className="text-gray-500 mb-6">
              尝试使用不同的关键词或检查拼写
            </p>
            <div className="space-x-4">
              <Button onClick={() => handleNewSearch('')}>
                清空搜索
              </Button>
              <Link href="/tools">
                <Button variant="outline">
                  浏览所有工具
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              开始搜索
            </h3>
            <p className="text-gray-500 mb-6">
              输入关键词搜索工具、网站或功能
            </p>
            <Link href="/tools">
              <Button>
                浏览所有工具
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

const SearchPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    }>
      <SearchResultsContent />
    </Suspense>
  );
};

export default SearchPage;
