'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle, Badge } from '@/components/ui';
import { MessageSquare, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface Feedback {
  _id: string;
  type: 'bug' | 'feature' | 'improvement' | 'question' | 'other';
  title: string;
  content: string;
  status: 'pending' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  adminReply?: string;
  adminReplyAt?: string;
  resolvedBy?: {
    username: string;
    email: string;
  };
}

interface FeedbackStats {
  pending?: number;
  'in-progress'?: number;
  resolved?: number;
  closed?: number;
}

const MyFeedback: React.FC = () => {
  const { data: session } = useSession();
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [stats, setStats] = useState<FeedbackStats>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  const fetchFeedbacks = async (page = 1, status = '') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
      });
      
      if (status) {
        params.append('status', status);
      }

      const response = await fetch(`/api/user/feedback?${params}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '获取反馈列表失败');
      }

      setFeedbacks(result.data.feedbacks);
      setStats(result.data.stats);
      setCurrentPage(result.data.pagination.page);
      setTotalPages(result.data.pagination.totalPages);
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      setError(error instanceof Error ? error.message : '获取反馈列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchFeedbacks(1, selectedStatus);
    }
  }, [session, selectedStatus]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'in-progress':
        return <AlertCircle className="w-4 h-4" />;
      case 'resolved':
        return <CheckCircle className="w-4 h-4" />;
      case 'closed':
        return <XCircle className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'in-progress':
        return '处理中';
      case 'resolved':
        return '已解决';
      case 'closed':
        return '已关闭';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'bug':
        return 'Bug报告';
      case 'feature':
        return '功能建议';
      case 'improvement':
        return '改进建议';
      case 'question':
        return '问题咨询';
      case 'other':
        return '其他';
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (!session?.user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">请先登录查看反馈记录</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-4">⏳</div>
        <p className="text-gray-500">加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-4">❌</div>
        <p className="text-red-500">{error}</p>
        <button
          onClick={() => fetchFeedbacks(1, selectedStatus)}
          className="mt-4 px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending || 0}</div>
            <div className="text-sm text-gray-600">待处理</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats['in-progress'] || 0}</div>
            <div className="text-sm text-gray-600">处理中</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.resolved || 0}</div>
            <div className="text-sm text-gray-600">已解决</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{stats.closed || 0}</div>
            <div className="text-sm text-gray-600">已关闭</div>
          </CardContent>
        </Card>
      </div>

      {/* 筛选器 */}
      <div className="flex space-x-2">
        <button
          onClick={() => setSelectedStatus('')}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            selectedStatus === '' 
              ? 'bg-primary-500 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部
        </button>
        {['pending', 'in-progress', 'resolved', 'closed'].map((status) => (
          <button
            key={status}
            onClick={() => setSelectedStatus(status)}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              selectedStatus === status 
                ? 'bg-primary-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {getStatusText(status)}
          </button>
        ))}
      </div>

      {/* 反馈列表 */}
      <div className="space-y-4">
        {feedbacks.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-4xl mb-4">📝</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无反馈记录</h3>
              <p className="text-gray-500 mb-4">您还没有提交过任何反馈</p>
              <button
                onClick={() => window.location.href = '/feedback'}
                className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600"
              >
                提交反馈
              </button>
            </CardContent>
          </Card>
        ) : (
          feedbacks.map((feedback) => (
            <Card key={feedback._id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{feedback.title}</CardTitle>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="outline">{getTypeText(feedback.type)}</Badge>
                      <Badge className={getStatusColor(feedback.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(feedback.status)}
                          <span>{getStatusText(feedback.status)}</span>
                        </div>
                      </Badge>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatDate(feedback.createdAt)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">反馈内容</h4>
                    <p className="text-gray-600 whitespace-pre-wrap">{feedback.content}</p>
                  </div>
                  
                  {feedback.adminReply && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">管理员回复</h4>
                      <p className="text-blue-800 whitespace-pre-wrap">{feedback.adminReply}</p>
                      {feedback.adminReplyAt && (
                        <div className="text-sm text-blue-600 mt-2">
                          回复时间: {formatDate(feedback.adminReplyAt)}
                          {feedback.resolvedBy && (
                            <span className="ml-2">
                              - {feedback.resolvedBy.username}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <button
            onClick={() => fetchFeedbacks(currentPage - 1, selectedStatus)}
            disabled={currentPage <= 1}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            上一页
          </button>
          <span className="px-4 py-2 text-gray-600">
            第 {currentPage} 页，共 {totalPages} 页
          </span>
          <button
            onClick={() => fetchFeedbacks(currentPage + 1, selectedStatus)}
            disabled={currentPage >= totalPages}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
};

export default MyFeedback;
