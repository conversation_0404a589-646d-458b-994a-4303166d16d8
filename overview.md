# Tool List

## 概览

我准备开发一个网站，这里网站包含我的常用工具列表。
比如：Unix timestamp转换工具，json格式化工具，文本大小写转换工具，导航网站等等。
（细节待拓展）

## 框架

网站会包含各个模块，每个工具会作为一个单独的模块存在（考虑到以后还会添加更多的模块进去，这样会方便管理、拓展）。
各个模块可以对外暴露访问API。有些模块的访问权限会根据用户的权限进行控制（默认大家都能访问，有些模块需要登录后才能访问）。
模块之间会通过RESTful API进行通信。
例如：网站存在登录模块；建议反馈模块；图片管理模块；图片管理模块；mongodb数据库管理模块等等。
（细节待拓展）

## 实现思路

先实现基础框架，再实现各个模块。每个模块之间的代码尽量相互独立，互不影响，最好一个模块一个文件夹。
（细节待拓展）

## 技术选型

开发框架：以nextjs+typescript作为主要开发框架（较为熟悉。有看过基础的开发文档）（选择原因：因为前端我对js、typescript比较熟悉，之前做过React+ts的简单项目，而nextjs是基于React的，且同时支持客户端+服务器开发）
数据库：mongodb（熟悉。这是我在工作开发使用的数据库）
部署：Vercel。此外，有必要的话，会使用阿里云服务器centos7提供图片管理服务器（例如：图片上传下载api的实现）。
其它：如果有特定的后端需求，可以使用golang（熟悉。这是我平常工作开发使用的编程语言）（比如：只是单纯地实现图片上传下载，可以使用golang实现，然后部署在云服务器的centos7上，对外暴露api接口提供服务特性）
（细节待拓展）

## 模块列表

1. 登录模块
2. 建议反馈模块
3. 图片管理模块
4. 时间转换模块（可以拆分为：unixtime时间戳转换；标准/unixtime时间差计算等）
5. json格式化模块
6. ip地址转换模块
7. 导航网站模块
8. 文本大小写转换模块（包含：大写转小写；小写转大写；下划线转大驼峰；大驼峰转下划线等等）
   （细节待拓展）

## ui 设计稿

支持pc和手机端
（细节待拓展）

## 测试用例

（细节待拓展）
