# MongoDB 数据库设置指南

本文档介绍如何为 Tool List 项目设置和配置 MongoDB 数据库。

## 📋 目录

- [本地 MongoDB 设置](#本地-mongodb-设置)
- [MongoDB Atlas 云数据库设置](#mongodb-atlas-云数据库设置)
- [环境变量配置](#环境变量配置)
- [数据库初始化](#数据库初始化)
- [数据模型](#数据模型)
- [API 端点](#api-端点)
- [故障排除](#故障排除)

## 🏠 本地 MongoDB 设置

### 1. 安装 MongoDB

#### Windows
1. 下载 [MongoDB Community Server](https://www.mongodb.com/try/download/community)
2. 运行安装程序，选择 "Complete" 安装
3. 安装 MongoDB Compass（可选的图形界面工具）

#### macOS
```bash
# 使用 Homebrew
brew tap mongodb/brew
brew install mongodb-community
```

#### Linux (Ubuntu/Debian)
```bash
# 导入公钥
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# 添加 MongoDB 源
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# 安装 MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org
```

### 2. 启动 MongoDB 服务

#### Windows
MongoDB 通常会作为 Windows 服务自动启动。如果没有：
```cmd
net start MongoDB
```

#### macOS
```bash
brew services start mongodb/brew/mongodb-community
```

#### Linux
```bash
sudo systemctl start mongod
sudo systemctl enable mongod  # 开机自启
```

### 3. 验证安装
```bash
# 连接到 MongoDB
mongosh

# 在 MongoDB shell 中
show dbs
```

## ☁️ MongoDB Atlas 云数据库设置

### 1. 创建账户
1. 访问 [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. 注册免费账户
3. 创建新的项目

### 2. 创建集群
1. 点击 "Build a Database"
2. 选择 "FREE" 共享集群
3. 选择云提供商和区域（推荐选择离您最近的区域）
4. 集群名称保持默认或自定义

### 3. 配置数据库访问
1. **创建数据库用户**：
   - 点击 "Database Access"
   - 点击 "Add New Database User"
   - 选择 "Password" 认证
   - 输入用户名和密码
   - 选择 "Built-in Role" -> "Read and write to any database"

2. **配置网络访问**：
   - 点击 "Network Access"
   - 点击 "Add IP Address"
   - 选择 "Allow Access from Anywhere" (0.0.0.0/0) 或添加您的特定 IP

### 4. 获取连接字符串
1. 点击 "Database" -> "Connect"
2. 选择 "Connect your application"
3. 选择 "Node.js" 和版本 "4.1 or later"
4. 复制连接字符串

## ⚙️ 环境变量配置

在项目根目录的 `.env.local` 文件中配置数据库连接：

### 本地 MongoDB
```env
# 本地 MongoDB
MONGODB_URI=mongodb://localhost:27017/toollist
MONGODB_DB=toollist
```

### MongoDB Atlas
```env
# MongoDB Atlas
MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.xxxxx.mongodb.net/toollist?retryWrites=true&w=majority
MONGODB_DB=toollist
```

**注意**：将 `<username>` 和 `<password>` 替换为您在 Atlas 中创建的用户凭据。

## 🚀 数据库初始化

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 测试数据库连接
访问：`http://localhost:3000/api/db/test`

应该返回类似以下的 JSON：
```json
{
  "success": true,
  "message": "MongoDB 连接测试成功",
  "data": {
    "status": "connected",
    "connectionTime": "150ms",
    "database": "toollist"
  }
}
```

### 3. 初始化数据库数据
访问数据库管理页面：`http://localhost:3000/admin/database`

或者通过 API：
```bash
# 初始化基础数据
curl -X POST http://localhost:3000/api/db/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "seed", "confirm": true}'

# 创建测试数据
curl -X POST http://localhost:3000/api/db/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "test-data"}'
```

## 📊 数据模型

### User (用户)
```typescript
{
  email: string;           // 邮箱
  name: string;           // 姓名
  role: 'user' | 'admin'; // 角色
  favoriteTools: string[]; // 收藏的工具
  recentTools: Array<{    // 最近使用的工具
    toolId: string;
    lastUsed: Date;
  }>;
  preferences: {          // 用户偏好
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
  };
}
```

### Tool (工具)
```typescript
{
  name: string;           // 工具名称
  description: string;    // 描述
  icon: string;          // 图标
  category: string;      // 分类
  tags: string[];        // 标签
  path: string;          // 路径
  isPublic: boolean;     // 是否公开
  visitCount: number;    // 访问次数
  status: 'active' | 'inactive' | 'maintenance';
}
```

### ToolUsage (使用统计)
```typescript
{
  toolId: string;        // 工具ID
  userId?: string;       // 用户ID（可选）
  action: 'view' | 'use' | 'download' | 'share';
  metadata: {            // 元数据
    userAgent?: string;
    ip?: string;
    duration?: number;
  };
}
```

## 🔌 API 端点

### 数据库测试
- `GET /api/db/test` - 测试数据库连接
- `POST /api/db/test` - 执行数据库操作（ping, stats）

### 数据库管理
- `GET /api/db/manage?action=stats` - 获取数据库统计
- `POST /api/db/manage` - 执行管理操作
  - `action: "seed"` - 初始化数据库
  - `action: "test-data"` - 创建测试数据
  - `action: "clear"` - 清空数据库（仅开发环境）

### 管理界面
- `/admin/database` - 数据库管理页面

## 🔧 故障排除

### 连接失败
1. **检查 MongoDB 服务是否运行**：
   ```bash
   # Windows
   net start MongoDB
   
   # macOS
   brew services start mongodb/brew/mongodb-community
   
   # Linux
   sudo systemctl status mongod
   ```

2. **检查端口是否被占用**：
   ```bash
   netstat -an | grep 27017
   ```

3. **检查防火墙设置**：
   确保端口 27017 没有被防火墙阻止

### Atlas 连接问题
1. **检查网络访问设置**：
   - 确保您的 IP 地址在白名单中
   - 或者允许所有 IP 访问 (0.0.0.0/0)

2. **检查用户权限**：
   - 确保数据库用户有读写权限
   - 检查用户名和密码是否正确

3. **检查连接字符串**：
   - 确保替换了 `<username>` 和 `<password>`
   - 确保数据库名称正确

### 常见错误

#### `MongooseServerSelectionError`
- 检查 MongoDB 服务是否运行
- 检查连接字符串是否正确
- 检查网络连接

#### `Authentication failed`
- 检查用户名和密码
- 确保用户有正确的权限

#### `ECONNREFUSED`
- MongoDB 服务未运行
- 端口被防火墙阻止
- 连接字符串中的主机或端口错误

## 📝 开发建议

1. **使用 MongoDB Compass** 进行数据库可视化管理
2. **定期备份数据**，特别是生产环境
3. **监控数据库性能**，使用 Atlas 的监控功能
4. **设置适当的索引**以提高查询性能
5. **使用环境变量**管理不同环境的配置

## 🔒 安全建议

1. **不要在代码中硬编码密码**
2. **使用强密码**和复杂的用户名
3. **限制网络访问**，只允许必要的 IP 地址
4. **定期更新密码**
5. **启用 MongoDB 的安全功能**（认证、授权等）
6. **在生产环境中使用 SSL/TLS**

## 📚 相关资源

- [MongoDB 官方文档](https://docs.mongodb.com/)
- [Mongoose 文档](https://mongoosejs.com/docs/)
- [MongoDB Atlas 文档](https://docs.atlas.mongodb.com/)
- [MongoDB Compass](https://www.mongodb.com/products/compass)
