'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from './IOPanel';
import { TextConverter as Converter } from '@/lib/utils/tools';
import { TextConverterState, ToolComponentProps } from '@/types/tools';
import ShareButton from '@/components/share/ShareButton';

const TextConverter: React.FC<ToolComponentProps> = ({
  config,
  onUsage
}) => {
  const [state, setState] = useState<TextConverterState>({
    input: '',
    output: '',
    operation: 'uppercase',
  });

  const [error, setError] = useState<string>('');

  // 实时转换 - 使用防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      if (state.input) {
        const result = Converter.convert(state);
        if (result.success) {
          setState(prev => ({ ...prev, output: result.data || '' }));
          setError('');

          // 记录使用
          onUsage?.('use', {
            operation: state.operation,
            inputLength: state.input.length,
            outputLength: result.data?.length || 0,
          });
        } else {
          setError(result.error || '转换失败');
          setState(prev => ({ ...prev, output: '' }));
        }
      } else {
        setState(prev => ({ ...prev, output: '' }));
        setError('');
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [state.input, state.operation]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleInputChange = (value: string) => {
    setState(prev => ({ ...prev, input: value }));
  };

  const handleClear = () => {
    setState(prev => ({ ...prev, input: '', output: '' }));
    setError('');
  };

  const handleExample = () => {
    const examples = {
      uppercase: 'Hello World! This is a sample text.',
      lowercase: 'HELLO WORLD! THIS IS A SAMPLE TEXT.',
      capitalize: 'hello world! this is a sample text.',
      camelCase: 'hello world sample text',
      snakeCase: 'Hello World Sample Text',
      kebabCase: 'Hello World Sample Text',
      base64Encode: 'Hello World!',
      base64Decode: 'SGVsbG8gV29ybGQh',
      urlEncode: 'Hello World! @#$%^&*()',
      urlDecode: 'Hello%20World%21%20%40%23%24%25%5E%26*%28%29',
      htmlEncode: '<div>Hello & "World"</div>',
      htmlDecode: '&lt;div&gt;Hello &amp; &quot;World&quot;&lt;/div&gt;',
    };

    setState(prev => ({
      ...prev,
      input: examples[state.operation] || 'Hello World!'
    }));
  };

  const handleCopyOutput = async () => {
    if (state.output) {
      try {
        await navigator.clipboard.writeText(state.output);
        onUsage?.('copy');
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !state.output
    },
  ];

  const operationGroups = [
    {
      title: '大小写转换',
      operations: [
        { key: 'uppercase', label: '转大写', description: '将所有字母转换为大写' },
        { key: 'lowercase', label: '转小写', description: '将所有字母转换为小写' },
        { key: 'capitalize', label: '首字母大写', description: '首字母大写，其余小写' },
      ]
    },
    {
      title: '命名格式转换',
      operations: [
        { key: 'camelCase', label: '驼峰命名', description: '转换为驼峰命名格式' },
        { key: 'snakeCase', label: '下划线命名', description: '转换为下划线命名格式' },
        { key: 'kebabCase', label: '短横线命名', description: '转换为短横线命名格式' },
      ]
    },
    {
      title: '编码转换',
      operations: [
        { key: 'base64Encode', label: 'Base64编码', description: '将文本编码为Base64格式' },
        { key: 'base64Decode', label: 'Base64解码', description: '将Base64格式解码为文本' },
        { key: 'urlEncode', label: 'URL编码', description: '将文本编码为URL安全格式' },
        { key: 'urlDecode', label: 'URL解码', description: '将URL编码解码为文本' },
        { key: 'htmlEncode', label: 'HTML编码', description: '将特殊字符编码为HTML实体' },
        { key: 'htmlDecode', label: 'HTML解码', description: '将HTML实体解码为特殊字符' },
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* 操作选择 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>转换操作</CardTitle>
            {state.input && state.output && !error && (
              <ShareButton
                toolId="text-converter"
                toolName="文本转换工具"
                input={state.input}
                output={state.output}
                options={{
                  operation: state.operation,
                  operationLabel: operationGroups
                    .flatMap(g => g.operations)
                    .find(op => op.key === state.operation)?.label,
                  inputLength: state.input.length,
                  outputLength: state.output.length,
                  wordCount: state.input.split(/\s+/).length,
                }}
                size="sm"
                showText={false}
              />
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {operationGroups.map((group) => (
              <div key={group.title}>
                <h4 className="text-sm font-medium text-gray-900 mb-3">{group.title}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {group.operations.map((op) => (
                    <label
                      key={op.key}
                      className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                        state.operation === op.key
                          ? 'border-primary-600 ring-2 ring-primary-600 bg-primary-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <input
                        type="radio"
                        name="operation"
                        value={op.key}
                        checked={state.operation === op.key}
                        onChange={(e) => setState(prev => ({
                          ...prev,
                          operation: e.target.value as TextConverterState['operation']
                        }))}
                        className="sr-only"
                      />
                      <div className="flex flex-col">
                        <span className="block text-sm font-medium text-gray-900">
                          {op.label}
                        </span>
                        <span className="block text-sm text-gray-500">
                          {op.description}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 输入输出面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入面板 */}
        <Card className="h-96">
          <IOPanel
            title="文本输入"
            placeholder="请输入要转换的文本..."
            value={state.input}
            onChange={handleInputChange}
            actions={inputActions}
            maxLength={config?.maxInputSize || 524288}
            error={error}
          />
        </Card>

        {/* 输出面板 */}
        <Card className="h-96">
          <IOPanel
            title="转换结果"
            placeholder="转换结果将显示在这里..."
            value={state.output}
            readonly
            actions={outputActions}
          />
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {operationGroups.flatMap(group => group.operations).map((op) => (
              <Button
                key={op.key}
                variant={state.operation === op.key ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setState(prev => ({ ...prev, operation: op.key as TextConverterState['operation'] }))}
                className="text-xs"
              >
                {op.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>大小写转换:</strong> 适用于文本格式化和标准化</p>
            <p><strong>命名格式转换:</strong> 适用于编程中的变量和函数命名</p>
            <p><strong>编码转换:</strong> 适用于数据传输和存储</p>
            <p><strong>支持功能:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>实时转换，输入即转换</li>
              <li>支持大文本处理（最大512KB）</li>
              <li>一键复制转换结果</li>
              <li>多种常用转换操作</li>
              <li>智能错误提示</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextConverter;
