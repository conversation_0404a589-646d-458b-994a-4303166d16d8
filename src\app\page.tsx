import Link from 'next/link';
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { TOOL_CATEGORIES, TOOLS } from '@/lib/constants/tools';
import { Analytics } from '@vercel/analytics/next';
import SearchBox from '@/components/search/SearchBox';
import StructuredDataComponent from '@/components/seo/StructuredData';
import { websiteStructuredData, softwareApplicationStructuredData } from '@/lib/structured-data';

export default function Home() {
  return (
    <>
      <div className="container mx-auto py-8">
        {/* Hero Section */}
        <section className="text-center py-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            软件工具集合
            {/* 软件开发者工具集合 */}
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              一个集成多种常用开发工具的在线平台，提供便捷的工具服务。
            {/* 一个集成多种常用开发工具的在线平台，提供便捷的工具服务，支持用户权限管理和模块化扩展 */}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/tools">开始使用</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/about">了解更多</Link>
            </Button>
          </div>
        </section>

        {/* Quick Search */}
        <section className="mb-16">
          <div className="max-w-2xl mx-auto">
            <SearchBox
              placeholder="搜索工具、网站或功能..."
              className="w-full"
              showSuggestions={true}
            />
          </div>
        </section>

        {/* Tool Categories */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            工具分类
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {TOOL_CATEGORIES.map((category) => (
              <Link key={category.id} href={`/tools?category=${category.id}`}>
                <Card variant="tool" className="group cursor-pointer hover:shadow-lg transition-all duration-200">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl"
                        style={{ backgroundColor: category.color }}
                      >
                        <span>{category.icon}</span>
                      </div>
                      <div>
                        <CardTitle className="group-hover:text-primary-600 transition-colors">
                          {category.name}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{category.description}</CardDescription>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* Popular Tools */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            热门工具
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {TOOLS.slice(0, 6).map((tool) => (
              <Card key={tool.id} variant="tool" className="group">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                        <span className="text-primary-600 text-lg">{tool.icon}</span>
                      </div>
                      <CardTitle className="group-hover:text-primary-600 transition-colors">
                        {tool.name}
                      </CardTitle>
                    </div>
                    {tool.requiredAuth && (
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        需登录
                      </span>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4">{tool.description}</CardDescription>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {tool.tags.map((tag) => (
                      <span
                        key={tag}
                        className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <Link href={tool.path}>使用工具</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <Button variant="outline" size="lg" asChild>
              <Link href="/tools">查看所有工具</Link>
            </Button>
          </div>
        </section>

        {/* Features */}
        <section className="py-16 bg-white rounded-2xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              为什么选择 Tool List？
            </h2>
            <p className="text-lg text-gray-600">
              我们致力于为开发者提供最好的工具体验
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">快速高效</h3>
              <p className="text-gray-600">
                所有工具都经过优化，确保快速响应和高效处理
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">安全可靠</h3>
              <p className="text-gray-600">
                数据处理完全在本地进行，保护您的隐私和数据安全
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">📱</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">响应式设计</h3>
              <p className="text-gray-600">
                完美适配各种设备，随时随地使用您需要的工具
              </p>
            </div>
          </div>
        </section>
      </div>
      <Analytics />
      <StructuredDataComponent data={[websiteStructuredData, softwareApplicationStructuredData]} />
    </>
  );
}
