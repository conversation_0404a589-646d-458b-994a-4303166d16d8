import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Tool List - 开发者工具集合',
    short_name: 'Tool List',
    description: '专业的在线开发工具集合平台，提供11个实用工具，支持工具分享和智能搜索。',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'zh-CN',
    categories: ['productivity', 'developer', 'utilities'],
    icons: [
      {
        src: '/icon',
        sizes: '32x32',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/apple-icon',
        sizes: '180x180',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/app-icon.svg',
        sizes: '192x192',
        type: 'image/svg+xml',
        purpose: 'any',
      },
    ],
    shortcuts: [
      {
        name: '时间戳转换',
        short_name: '时间戳',
        description: 'Unix时间戳转换工具',
        url: '/tools/timestamp',
        icons: [{ src: '/app-icon.svg', sizes: '192x192' }],
      },
      {
        name: 'JSON格式化',
        short_name: 'JSON',
        description: 'JSON格式化和验证工具',
        url: '/tools/json-formatter',
        icons: [{ src: '/app-icon.svg', sizes: '192x192' }],
      },
      {
        name: '搜索工具',
        short_name: '搜索',
        description: '搜索所有可用工具',
        url: '/search',
        icons: [{ src: '/app-icon.svg', sizes: '192x192' }],
      },
    ],
  };
}
