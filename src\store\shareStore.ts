import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  ShareLink, 
  ShareOptions, 
  ShareResult, 
  ShareStats,
  ShareableState,
  SOCIAL_PLATFORMS,
  SocialSharePlatform 
} from '@/types/share';
import { 
  createShareLink, 
  getShareLink, 
  getUserShares, 
  deleteShareLink 
} from '@/lib/share/shareService';

interface ShareState {
  // 分享状态
  isSharing: boolean;
  isLoading: boolean;
  
  // 分享数据
  currentShare: ShareLink | null;
  userShares: ShareLink[];
  shareStats: ShareStats;
  
  // 分享选项
  shareOptions: ShareOptions;
  
  // 社交平台
  socialPlatforms: SocialSharePlatform[];
  
  // Actions
  createShare: (state: ShareableState, options?: ShareOptions) => Promise<ShareResult>;
  loadShare: (shortId: string, password?: string) => Promise<ShareLink | null>;
  loadUserShares: (userId?: string) => Promise<void>;
  deleteShare: (shortId: string) => Promise<boolean>;
  
  // 分享选项管理
  updateShareOptions: (options: Partial<ShareOptions>) => void;
  resetShareOptions: () => void;
  
  // 社交分享
  shareToSocial: (platform: string, shareUrl: string, title: string, description?: string) => void;
  copyShareUrl: (url: string) => Promise<boolean>;
  
  // 统计相关
  updateStats: () => void;
  recordShareView: (shareId: string) => void;
  
  // 清理
  clearCurrentShare: () => void;
  clearUserShares: () => void;
}

const defaultShareOptions: ShareOptions = {
  includeOutput: true,
  setPassword: false,
  password: '',
  expirationDays: 30,
  isPublic: true,
  customMessage: '',
};

const defaultStats: ShareStats = {
  totalShares: 0,
  totalViews: 0,
  popularTools: [],
  recentShares: [],
};

export const useShareStore = create<ShareState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isSharing: false,
      isLoading: false,
      currentShare: null,
      userShares: [],
      shareStats: defaultStats,
      shareOptions: defaultShareOptions,
      socialPlatforms: SOCIAL_PLATFORMS,

      // 创建分享
      createShare: async (state: ShareableState, options?: ShareOptions) => {
        set({ isSharing: true });
        
        try {
          const finalOptions = { ...get().shareOptions, ...options };
          const result = await createShareLink(state, finalOptions);
          
          if (result.success) {
            // 更新用户分享列表
            await get().loadUserShares();
            
            // 更新统计
            get().updateStats();
          }
          
          return result;
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : '创建分享失败',
          };
        } finally {
          set({ isSharing: false });
        }
      },

      // 加载分享
      loadShare: async (shortId: string, password?: string) => {
        set({ isLoading: true });
        
        try {
          const shareLink = await getShareLink(shortId);
          
          if (shareLink) {
            // 验证密码
            if (shareLink.password && shareLink.password !== password) {
              throw new Error('密码错误');
            }
            
            set({ currentShare: shareLink });
            get().recordShareView(shareLink.id);
          }
          
          return shareLink;
        } catch (error) {
          console.error('加载分享失败:', error);
          return null;
        } finally {
          set({ isLoading: false });
        }
      },

      // 加载用户分享列表
      loadUserShares: async (userId?: string) => {
        try {
          const shares = await getUserShares(userId);
          set({ userShares: shares });
        } catch (error) {
          console.error('加载用户分享失败:', error);
        }
      },

      // 删除分享
      deleteShare: async (shortId: string) => {
        try {
          const success = await deleteShareLink(shortId);
          
          if (success) {
            // 更新用户分享列表
            const userShares = get().userShares.filter(share => share.shortId !== shortId);
            set({ userShares });
            
            // 更新统计
            get().updateStats();
          }
          
          return success;
        } catch (error) {
          console.error('删除分享失败:', error);
          return false;
        }
      },

      // 更新分享选项
      updateShareOptions: (options: Partial<ShareOptions>) => {
        set((state) => ({
          shareOptions: { ...state.shareOptions, ...options },
        }));
      },

      // 重置分享选项
      resetShareOptions: () => {
        set({ shareOptions: defaultShareOptions });
      },

      // 社交分享
      shareToSocial: (platform: string, shareUrl: string, title: string, description = '') => {
        const socialPlatform = get().socialPlatforms.find(p => p.id === platform);
        
        if (!socialPlatform || !socialPlatform.enabled) {
          console.error('不支持的社交平台:', platform);
          return;
        }

        // 特殊处理微信
        if (platform === 'wechat') {
          // 微信分享需要特殊处理，这里先复制链接
          get().copyShareUrl(shareUrl);
          alert('链接已复制，请在微信中粘贴分享');
          return;
        }

        // 构建分享URL
        let shareUrlTemplate = socialPlatform.urlTemplate;
        shareUrlTemplate = shareUrlTemplate.replace('{url}', encodeURIComponent(shareUrl));
        shareUrlTemplate = shareUrlTemplate.replace('{title}', encodeURIComponent(title));
        shareUrlTemplate = shareUrlTemplate.replace('{description}', encodeURIComponent(description));

        // 打开分享窗口
        const width = 600;
        const height = 400;
        const left = (window.screen.width - width) / 2;
        const top = (window.screen.height - height) / 2;
        
        window.open(
          shareUrlTemplate,
          'share',
          `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        );
      },

      // 复制分享链接
      copyShareUrl: async (url: string) => {
        try {
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(url);
            return true;
          } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = url;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
          }
        } catch (error) {
          console.error('复制失败:', error);
          return false;
        }
      },

      // 更新统计
      updateStats: () => {
        const userShares = get().userShares;
        
        const totalShares = userShares.length;
        const totalViews = userShares.reduce((sum, share) => sum + share.viewCount, 0);
        
        // 统计热门工具
        const toolCounts: Record<string, { toolId: string; toolName: string; shareCount: number }> = {};
        userShares.forEach(share => {
          if (!toolCounts[share.toolId]) {
            toolCounts[share.toolId] = {
              toolId: share.toolId,
              toolName: share.toolName,
              shareCount: 0,
            };
          }
          toolCounts[share.toolId].shareCount += 1;
        });
        
        const popularTools = Object.values(toolCounts)
          .sort((a, b) => b.shareCount - a.shareCount)
          .slice(0, 10);
        
        // 最近分享
        const recentShares = userShares
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 10);

        set({
          shareStats: {
            totalShares,
            totalViews,
            popularTools,
            recentShares,
          },
        });
      },

      // 记录分享查看
      recordShareView: (shareId: string) => {
        // 在实际应用中，这里应该发送到服务器
        console.log('分享被查看:', shareId);
      },

      // 清理当前分享
      clearCurrentShare: () => {
        set({ currentShare: null });
      },

      // 清理用户分享
      clearUserShares: () => {
        set({ userShares: [], shareStats: defaultStats });
      },
    }),
    {
      name: 'share-storage',
      version: 1,
      // 只持久化部分数据
      partialize: (state) => ({
        shareOptions: state.shareOptions,
        shareStats: state.shareStats,
      }),
    }
  )
);
