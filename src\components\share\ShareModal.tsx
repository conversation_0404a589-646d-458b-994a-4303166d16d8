'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { ShareableState, SOCIAL_PLATFORMS } from '@/types/share';
import { useShareStore } from '@/store/shareStore';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareState: ShareableState;
}

const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  shareState,
}) => {
  const [shareUrl, setShareUrl] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<'link' | 'social' | 'options'>('link');

  const {
    shareOptions,
    updateShareOptions,
    createShare,
    shareToSocial,
    copyShareUrl,
    isSharing,
  } = useShareStore();

  // 生成分享链接
  const handleCreateShare = async () => {
    const result = await createShare(shareState, shareOptions);

    if (result.success && result.fullUrl) {
      setShareUrl(result.fullUrl);
      setActiveTab('link');
    } else {
      alert(result.error || '创建分享链接失败');
    }
  };

  // 复制链接
  const handleCopyUrl = async () => {
    if (!shareUrl) return;

    const success = await copyShareUrl(shareUrl);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } else {
      alert('复制失败，请手动复制');
    }
  };

  // 社交分享
  const handleSocialShare = (platform: string) => {
    if (!shareUrl) {
      alert('请先生成分享链接');
      return;
    }

    const title = `${shareState.toolName} - 在线工具`;
    const description = shareState.output
      ? `查看我用 ${shareState.toolName} 的处理结果`
      : `试试这个实用的 ${shareState.toolName} 工具`;

    shareToSocial(platform, shareUrl, title, description);
  };

  // 重置状态
  useEffect(() => {
    if (isOpen) {
      setShareUrl('');
      setCopySuccess(false);
      setActiveTab('link');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 标题 */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">分享工具</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ✕
            </button>
          </div>

          {/* 工具信息 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">{shareState.toolName}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {shareState.input && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">输入：</span>
                    <p className="text-sm text-gray-800 bg-gray-50 p-2 rounded mt-1 max-h-20 overflow-y-auto">
                      {shareState.input.length > 100
                        ? `${shareState.input.substring(0, 100)}...`
                        : shareState.input}
                    </p>
                  </div>
                )}
                {shareState.output && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">输出：</span>
                    <p className="text-sm text-gray-800 bg-gray-50 p-2 rounded mt-1 max-h-20 overflow-y-auto">
                      {shareState.output.length > 100
                        ? `${shareState.output.substring(0, 100)}...`
                        : shareState.output}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 标签页 */}
          <div className="flex border-b border-gray-200 mb-6">
            <button
              onClick={() => setActiveTab('link')}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === 'link'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              生成链接
            </button>
            <button
              onClick={() => setActiveTab('social')}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === 'social'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              社交分享
            </button>
            <button
              onClick={() => setActiveTab('options')}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === 'options'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              分享设置
            </button>
          </div>

          {/* 生成链接标签页 */}
          {activeTab === 'link' && (
            <div className="space-y-4">
              {!shareUrl ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🔗</div>
                  <p className="text-gray-600 mb-4">点击按钮生成分享链接</p>
                  <Button
                    onClick={handleCreateShare}
                    disabled={isSharing}
                    className="px-6 py-2"
                  >
                    {isSharing ? '生成中...' : '生成分享链接'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      分享链接
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={shareUrl}
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                      />
                      <Button
                        onClick={handleCopyUrl}
                        variant="outline"
                        className={copySuccess ? 'bg-green-50 text-green-600' : ''}
                      >
                        {copySuccess ? '已复制' : '复制'}
                      </Button>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">分享说明</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• 链接有效期：{shareOptions.expirationDays} 天</li>
                      <li>• 包含输入和输出数据</li>
                      <li>• 任何人都可以通过此链接查看</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 社交分享标签页 */}
          {activeTab === 'social' && (
            <div className="space-y-4">
              {!shareUrl ? (
                <div className="text-center py-8">
                  <p className="text-gray-600">请先生成分享链接</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {SOCIAL_PLATFORMS.filter(p => p.enabled).map((platform) => (
                    <button
                      key={platform.id}
                      onClick={() => handleSocialShare(platform.id)}
                      className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                      style={{ borderColor: platform.color + '20' }}
                    >
                      <span className="text-2xl">{platform.icon}</span>
                      <span className="font-medium">{platform.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* 分享设置标签页 */}
          {activeTab === 'options' && (
            <div className="space-y-6">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={shareOptions.includeOutput}
                    onChange={(e) => updateShareOptions({ includeOutput: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">包含输出结果</span>
                </label>
                <p className="text-xs text-gray-500 mt-1">分享时包含工具的处理结果</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  有效期（天）
                </label>
                <select
                  value={shareOptions.expirationDays}
                  onChange={(e) => updateShareOptions({ expirationDays: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value={1}>1天</option>
                  <option value={7}>7天</option>
                  <option value={30}>30天</option>
                  <option value={90}>90天</option>
                  <option value={365}>1年</option>
                </select>
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={shareOptions.setPassword}
                    onChange={(e) => updateShareOptions({ setPassword: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">设置访问密码</span>
                </label>
                {shareOptions.setPassword && (
                  <input
                    type="password"
                    placeholder="输入密码"
                    value={shareOptions.password || ''}
                    onChange={(e) => updateShareOptions({ password: e.target.value })}
                    className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义消息
                </label>
                <textarea
                  placeholder="添加一些说明..."
                  value={shareOptions.customMessage || ''}
                  onChange={(e) => updateShareOptions({ customMessage: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
            {activeTab !== 'link' && !shareUrl && (
              <Button onClick={handleCreateShare} disabled={isSharing}>
                {isSharing ? '生成中...' : '生成链接'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
