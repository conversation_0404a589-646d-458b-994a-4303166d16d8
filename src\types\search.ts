// 搜索相关类型定义

export type SearchResultType = 'tool' | 'website' | 'page' | 'help';

export interface SearchResult {
  id: string;
  type: SearchResultType;
  title: string;
  description: string;
  url: string;
  icon?: string;
  category?: string;
  tags?: string[];
  relevanceScore: number; // 相关性评分 0-100
  lastUpdated?: Date;
}

export interface SearchQuery {
  query: string;
  type?: SearchResultType | 'all';
  category?: string;
  limit?: number;
  offset?: number;
}

export interface SearchFilters {
  type: SearchResultType | 'all';
  category: string | 'all';
  sortBy: 'relevance' | 'name' | 'category' | 'recent';
  sortOrder: 'asc' | 'desc';
}

export interface SearchHistory {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  clickedResult?: string; // 点击的结果ID
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'query' | 'tool' | 'category';
  frequency: number; // 使用频率
}

export interface SearchStats {
  totalSearches: number;
  popularQueries: string[];
  recentQueries: string[];
  noResultQueries: string[];
}

// 搜索配置
export interface SearchConfig {
  maxResults: number;
  maxSuggestions: number;
  maxHistory: number;
  enableFuzzySearch: boolean;
  enableAutoComplete: boolean;
  minQueryLength: number;
}

// 默认搜索配置
export const DEFAULT_SEARCH_CONFIG: SearchConfig = {
  maxResults: 50,
  maxSuggestions: 8,
  maxHistory: 100,
  enableFuzzySearch: true,
  enableAutoComplete: true,
  minQueryLength: 1,
};

// 搜索结果分类
export const SEARCH_CATEGORIES = {
  tool: {
    name: '工具',
    icon: '🔧',
    color: '#3b82f6',
  },
  website: {
    name: '网站',
    icon: '🌐',
    color: '#10b981',
  },
  page: {
    name: '页面',
    icon: '📄',
    color: '#f59e0b',
  },
  help: {
    name: '帮助',
    icon: '❓',
    color: '#8b5cf6',
  },
} as const;

// 热门搜索关键词
export const POPULAR_SEARCHES = [
  '时间戳',
  'JSON',
  '编码',
  '哈希',
  '颜色',
  '二维码',
  '图片压缩',
  'Base64',
  'URL编码',
  'IP转换',
];

// 搜索快捷方式
export const SEARCH_SHORTCUTS = [
  {
    key: 'timestamp',
    aliases: ['时间戳', 'unix', '时间转换'],
    target: '/tools/timestamp-converter',
  },
  {
    key: 'json',
    aliases: ['json格式化', 'json美化', 'json'],
    target: '/tools/json-formatter',
  },
  {
    key: 'base64',
    aliases: ['base64编码', 'base64解码', 'base64'],
    target: '/tools/base64-converter',
  },
  {
    key: 'hash',
    aliases: ['哈希', 'sha256', 'md5', '加密'],
    target: '/tools/sha-hash',
  },
  {
    key: 'color',
    aliases: ['颜色转换', '颜色', 'hex', 'rgb'],
    target: '/tools/color-converter',
  },
  {
    key: 'qr',
    aliases: ['二维码', 'qr码', '二维码生成'],
    target: '/tools/qr-generator',
  },
] as const;
