'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui';

export default function FeaturesPage() {
  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-gray-700">工具</Link>
          <span>/</span>
          <Link href="/tools/timestamp" className="hover:text-gray-700">时间戳转换器</Link>
          <span>/</span>
          <span className="text-gray-900">功能特性</span>
        </nav>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">功能特性</h1>
          <div className="flex space-x-2">
            <Link
              href="/tools/timestamp"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              🏠 返回转换器
            </Link>
            <Link
              href="/tools/case-converter"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              🔤 大小写转换
            </Link>
          </div>
        </div>
      </div>

      {/* 功能特性内容 */}
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              📋 智能复制系统
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>图标化复制按钮：</strong> 直观的 📋 图标而非文字</li>
              <li><strong>双击快捷操作：</strong> PC和移动端都支持双击显示快捷复制菜单</li>
              <li><strong>绿色成功反馈：</strong> 复制成功时显示视觉 ✓ 动画</li>
              <li><strong>防重复触发：</strong> 优化的双击逻辑，防止意外复制</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              🎯 快捷时间复制
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>预设时间间隔：</strong> 1分钟、3分钟、5分钟后的时间戳</li>
              <li><strong>自定义时间输入：</strong> 输入任意分钟数，自动缓存用户偏好</li>
              <li><strong>实时计算：</strong> 动态计算未来时间戳，支持秒和毫秒</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              🌍 多语言支持
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>10种语言：</strong> 英语、中文、印地语、日语、德语、英式英语、俄语、韩语、加拿大英语、法语</li>
              <li><strong>智能提示：</strong> PC显示&ldquo;双击复制按钮快速复制&rdquo;，移动端也支持双击</li>
              <li><strong>URL路径支持：</strong> 多语言URL路径访问（如 /zh-CN/, /ja-JP/）</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              📱 移动端优化
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>触摸友好：</strong> 优化的移动端触摸体验，支持双击操作</li>
              <li><strong>响应式设计：</strong> 完美适配各种屏幕尺寸</li>
              <li><strong>华为浏览器兼容：</strong> 特别优化华为手机浏览器的复制功能</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              🔧 技术特性
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>多层复制降级：</strong> 现代 Clipboard API → execCommand → 手动复制对话框</li>
              <li><strong>智能格式检测：</strong> 自动识别秒、毫秒、微秒、纳秒时间戳格式</li>
              <li><strong>实时转换：</strong> 输入即转换，无需点击按钮</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              ⚡ 性能与可靠性
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>轻量级：</strong> 纯 HTML/CSS/JavaScript，无外部依赖</li>
              <li><strong>快速加载：</strong> 优化的页面加载时间</li>
              <li><strong>跨浏览器：</strong> 在所有现代浏览器上正常工作</li>
              <li><strong>离线可用：</strong> 核心功能无需网络连接</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              🎨 用户体验
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li><strong>简洁界面：</strong> 专注功能的极简设计</li>
              <li><strong>直观导航：</strong> 易于理解和使用</li>
              <li><strong>无障碍访问：</strong> 支持键盘导航和屏幕阅读器</li>
              <li><strong>视觉反馈：</strong> 所有用户操作都有清晰的指示</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* 返回按钮 */}
      <div className="mt-8 text-center">
        <Link
          href="/tools/timestamp"
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          ← 返回时间戳转换器
        </Link>
      </div>
    </div>
  );
}
