'use client';

import React, { useState, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useUserPreferencesStore } from '@/store/userPreferencesStore';
import { TOOLS } from '@/lib/constants/tools';
import Link from 'next/link';

const HistoryPage: React.FC = () => {
  const { data: session } = useSession();
  const { usageHistory, stats, clearHistory } = useUserPreferencesStore();
  const [filterTool, setFilterTool] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'tool' | 'action'>('date');

  // 过滤和排序历史记录
  const filteredHistory = useMemo(() => {
    let filtered = usageHistory;

    // 按工具过滤
    if (filterTool !== 'all') {
      filtered = filtered.filter(record => record.toolId === filterTool);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.usedAt).getTime() - new Date(a.usedAt).getTime();
        case 'tool':
          return a.toolName.localeCompare(b.toolName);
        case 'action':
          return a.action.localeCompare(b.action);
        default:
          return 0;
      }
    });

    return filtered;
  }, [usageHistory, filterTool, sortBy]);

  // 获取使用过的工具列表
  const usedTools = useMemo(() => {
    const toolIds = [...new Set(usageHistory.map(record => record.toolId))];
    return toolIds.map(toolId => TOOLS.find(tool => tool.id === toolId)).filter(Boolean);
  }, [usageHistory]);

  // 统计数据
  const todayUsage = usageHistory.filter(record => {
    const today = new Date().toDateString();
    return new Date(record.usedAt).toDateString() === today;
  }).length;

  // const thisWeekUsage = usageHistory.filter(record => {
  //   const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  //   return new Date(record.usedAt) > weekAgo;
  // }).length;

  if (!session) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">请先登录</h1>
          <p className="text-gray-600 mb-6">
            您需要登录后才能查看使用历史
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            前往登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">使用历史</h1>
          <p className="text-gray-600">
            查看您的工具使用记录和统计信息
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stats.totalUsageCount}
              </div>
              <div className="text-gray-600">总使用次数</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {stats.totalToolsUsed}
              </div>
              <div className="text-gray-600">使用过的工具</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {todayUsage}
              </div>
              <div className="text-gray-600">今日使用</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {stats.streakDays}
              </div>
              <div className="text-gray-600">连续使用天数</div>
            </CardContent>
          </Card>
        </div>

        {/* 过滤和排序 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>筛选和排序</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  按工具筛选
                </label>
                <select
                  value={filterTool}
                  onChange={(e) => setFilterTool(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">全部工具</option>
                  {usedTools.map((tool) => (
                    <option key={tool?.id} value={tool?.id}>
                      {tool?.icon} {tool?.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  排序方式
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'tool' | 'action')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="date">按时间排序</option>
                  <option value="tool">按工具排序</option>
                  <option value="action">按操作排序</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (confirm('确定要清空所有使用历史吗？此操作不可撤销。')) {
                      clearHistory();
                    }
                  }}
                >
                  清空历史
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 历史记录列表 */}
        <Card>
          <CardHeader>
            <CardTitle>
              使用记录 ({filteredHistory.length} 条)
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredHistory.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">暂无使用记录</h3>
                <p className="text-gray-500 mb-6">
                  开始使用工具后，这里会显示您的使用历史
                </p>
                <Link href="/tools">
                  <Button>浏览工具</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredHistory.map((record) => {
                  const tool = TOOLS.find(t => t.id === record.toolId);
                  return (
                    <div
                      key={record.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="text-2xl">{tool?.icon || '🔧'}</div>
                        <div>
                          <div className="font-medium">{record.toolName}</div>
                          <div className="text-sm text-gray-500">{record.action}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-900">
                          {new Date(record.usedAt).toLocaleDateString('zh-CN')}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(record.usedAt).toLocaleTimeString('zh-CN')}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 返回按钮 */}
        <div className="mt-8 text-center">
          <Link href="/profile">
            <Button variant="outline">
              ← 返回个人中心
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HistoryPage;
