'use client';

import { useState, useCallback } from 'react';

interface CopyState {
  show: boolean;
  message: string;
  id: number;
}

interface UseCopyToClipboardReturn {
  copyState: CopyState;
  copyToClipboard: (text: string, customMessage?: string) => Promise<boolean>;
  hideCopyToast: () => void;
}

export const useCopyToClipboard = (): UseCopyToClipboardReturn => {
  const [copyState, setCopyState] = useState<CopyState>({
    show: false,
    message: '',
    id: 0
  });

  const showCopySuccess = useCallback((text: string, customMessage?: string) => {
    const id = Date.now();

    // 截断长文本，最多显示30个字符
    let displayText = customMessage || text;
    if (displayText.length > 30) {
      displayText = displayText.substring(0, 30) + '...';
    }

    // 如果当前已经有提示在显示，先隐藏它
    setCopyState(() => ({ show: false, message: '', id: 0 }));

    // 短暂延迟后显示新提示，确保动画重新开始
    setTimeout(() => {
      setCopyState({
        show: true,
        message: displayText,
        id
      });
    }, 50);

    // 3秒后自动隐藏
    setTimeout(() => {
      setCopyState(prev => prev.id === id ? { show: false, message: '', id: 0 } : prev);
    }, 3050);
  }, []);

  const copyToClipboard = useCallback(async (text: string, customMessage?: string): Promise<boolean> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        showCopySuccess(text, customMessage);
        return true;
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          showCopySuccess(text, customMessage);
          return true;
        } else {
          showCopySuccess('', '复制失败，请手动复制');
          return false;
        }
      }
    } catch (err) {
      console.error('复制失败:', err);
      showCopySuccess('', '复制失败，请手动复制');
      return false;
    }
  }, [showCopySuccess]);

  const hideCopyToast = useCallback(() => {
    setCopyState({ show: false, message: '', id: 0 });
  }, []);

  return {
    copyState,
    copyToClipboard,
    hideCopyToast
  };
};
