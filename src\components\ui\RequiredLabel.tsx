import React from 'react';

interface RequiredLabelProps {
  children: React.ReactNode;
  required?: boolean;
  className?: string;
}

const RequiredLabel: React.FC<RequiredLabelProps> = ({ 
  children, 
  required = false, 
  className = '' 
}) => {
  return (
    <label className={`block text-sm font-medium text-gray-700 ${className}`}>
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
};

export default RequiredLabel;
