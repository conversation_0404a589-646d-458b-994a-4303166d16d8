'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchStore } from '@/store/searchStore';
import { Button } from '@/components/ui';

interface SearchBoxProps {
  placeholder?: string;
  showSuggestions?: boolean;
  autoFocus?: boolean;
  onSearch?: (query: string) => void;
  className?: string;
  initialValue?: string; // 初始值
  controlled?: boolean; // 是否受控组件
}

const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = '搜索工具、网站或功能...',
  showSuggestions = true,
  autoFocus = false,
  onSearch,
  className = '',
  initialValue = '',
  controlled = false,
}) => {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [localQuery, setLocalQuery] = useState(initialValue);

  const {
    query,
    suggestions,
    history,
    setQuery,
    search,
    clearSuggestions,
  } = useSearchStore();

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // 初始化时设置值
  useEffect(() => {
    if (initialValue && !localQuery) {
      setLocalQuery(initialValue);
    }
  }, [initialValue, localQuery]);

  // 只有在非受控模式下才同步 store 的 query
  useEffect(() => {
    if (!controlled && query !== localQuery) {
      setLocalQuery(query);
    }
  }, [query, controlled, localQuery]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalQuery(value);
    setQuery(value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (localQuery.trim()) {
      performSearch(localQuery.trim());
    }
  };

  const performSearch = (searchQuery: string) => {
    search(searchQuery);
    clearSuggestions();
    setIsFocused(false);

    if (onSearch) {
      onSearch(searchQuery);
    } else {
      // 导航到搜索结果页面
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setLocalQuery(suggestion);
    setQuery(suggestion);
    performSearch(suggestion);
  };

  const handleHistoryClick = (historyItem: string) => {
    setLocalQuery(historyItem);
    setQuery(historyItem);
    performSearch(historyItem);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsFocused(false);
      clearSuggestions();
      inputRef.current?.blur();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    // 延迟隐藏建议，以便点击建议项
    setTimeout(() => {
      setIsFocused(false);
    }, 200);
  };

  const showDropdown = isFocused && showSuggestions && (suggestions.length > 0 || history.length > 0);
  const recentSearches = history.slice(0, 5);

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={localQuery}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />

          {/* 搜索图标 */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            🔍
          </div>

          {/* 清空按钮 */}
          {localQuery && (
            <button
              type="button"
              onClick={() => {
                setLocalQuery('');
                setQuery('');
                clearSuggestions();
                inputRef.current?.focus();
              }}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}

          {/* 搜索按钮 */}
          <Button
            type="submit"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2"
            disabled={!localQuery.trim()}
          >
            搜索
          </Button>
        </div>
      </form>

      {/* 建议下拉框 */}
      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* 搜索建议 */}
          {suggestions.length > 0 && (
            <div className="p-2">
              <div className="text-xs text-gray-500 px-2 py-1 font-medium">搜索建议</div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded-md flex items-center space-x-2"
                >
                  <span className="text-gray-400">🔍</span>
                  <span>{suggestion}</span>
                </button>
              ))}
            </div>
          )}

          {/* 分隔线 */}
          {suggestions.length > 0 && recentSearches.length > 0 && (
            <div className="border-t border-gray-100"></div>
          )}

          {/* 最近搜索 */}
          {recentSearches.length > 0 && (
            <div className="p-2">
              <div className="text-xs text-gray-500 px-2 py-1 font-medium">最近搜索</div>
              {recentSearches.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleHistoryClick(item.query)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded-md flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400">🕒</span>
                    <span>{item.query}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {item.resultCount} 个结果
                  </span>
                </button>
              ))}
            </div>
          )}

          {/* 空状态 */}
          {suggestions.length === 0 && recentSearches.length === 0 && localQuery && (
            <div className="p-4 text-center text-gray-500">
              <div className="text-2xl mb-2">🔍</div>
              <div>开始输入以获取搜索建议</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBox;
