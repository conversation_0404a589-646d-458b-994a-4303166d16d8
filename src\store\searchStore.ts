import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  SearchResult, 
  SearchQuery, 
  SearchFilters, 
  SearchHistory, 
  SearchStats,
  DEFAULT_SEARCH_CONFIG 
} from '@/types/search';
import { searchItems, getSearchSuggestions } from '@/lib/search/searchEngine';

interface SearchState {
  // 搜索状态
  query: string;
  results: SearchResult[];
  suggestions: string[];
  isSearching: boolean;
  
  // 搜索历史
  history: SearchHistory[];
  
  // 搜索过滤器
  filters: SearchFilters;
  
  // 搜索统计
  stats: SearchStats;
  
  // 配置
  config: typeof DEFAULT_SEARCH_CONFIG;
  
  // Actions
  setQuery: (query: string) => void;
  search: (query: string) => Promise<void>;
  clearSearch: () => void;
  
  // 建议相关
  updateSuggestions: (query: string) => void;
  clearSuggestions: () => void;
  
  // 过滤器相关
  updateFilters: (filters: Partial<SearchFilters>) => void;
  resetFilters: () => void;
  
  // 历史相关
  addToHistory: (query: string, resultCount: number) => void;
  clearHistory: () => void;
  removeFromHistory: (id: string) => void;
  
  // 统计相关
  recordSearch: (query: string, resultCount: number) => void;
  recordClick: (resultId: string, query: string) => void;
  
  // 配置相关
  updateConfig: (config: Partial<typeof DEFAULT_SEARCH_CONFIG>) => void;
}

const defaultFilters: SearchFilters = {
  type: 'all',
  category: 'all',
  sortBy: 'relevance',
  sortOrder: 'desc',
};

const defaultStats: SearchStats = {
  totalSearches: 0,
  popularQueries: [],
  recentQueries: [],
  noResultQueries: [],
};

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      // 初始状态
      query: '',
      results: [],
      suggestions: [],
      isSearching: false,
      history: [],
      filters: defaultFilters,
      stats: defaultStats,
      config: DEFAULT_SEARCH_CONFIG,

      // 设置查询
      setQuery: (query: string) => {
        set({ query });
        
        // 更新建议
        if (query.length >= get().config.minQueryLength) {
          get().updateSuggestions(query);
        } else {
          set({ suggestions: [] });
        }
      },

      // 执行搜索
      search: async (query: string) => {
        if (!query.trim()) {
          set({ results: [], query: '' });
          return;
        }

        set({ isSearching: true, query });

        try {
          const searchQuery: SearchQuery = {
            query: query.trim(),
            limit: get().config.maxResults,
          };

          const results = searchItems(searchQuery, get().filters);
          
          set({ 
            results, 
            isSearching: false,
            suggestions: [] // 清空建议
          });

          // 记录搜索
          get().recordSearch(query, results.length);
          get().addToHistory(query, results.length);

        } catch (error) {
          console.error('搜索失败:', error);
          set({ 
            results: [], 
            isSearching: false 
          });
        }
      },

      // 清空搜索
      clearSearch: () => {
        set({
          query: '',
          results: [],
          suggestions: [],
          isSearching: false,
        });
      },

      // 更新建议
      updateSuggestions: (query: string) => {
        if (!get().config.enableAutoComplete) {
          return;
        }

        const suggestions = getSearchSuggestions(query);
        set({ suggestions: suggestions.slice(0, get().config.maxSuggestions) });
      },

      // 清空建议
      clearSuggestions: () => {
        set({ suggestions: [] });
      },

      // 更新过滤器
      updateFilters: (newFilters: Partial<SearchFilters>) => {
        const filters = { ...get().filters, ...newFilters };
        set({ filters });

        // 如果有当前查询，重新搜索
        const currentQuery = get().query;
        if (currentQuery) {
          get().search(currentQuery);
        }
      },

      // 重置过滤器
      resetFilters: () => {
        set({ filters: defaultFilters });
        
        // 如果有当前查询，重新搜索
        const currentQuery = get().query;
        if (currentQuery) {
          get().search(currentQuery);
        }
      },

      // 添加到历史
      addToHistory: (query: string, resultCount: number) => {
        const history = get().history;
        const newRecord: SearchHistory = {
          id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          query: query.trim(),
          timestamp: new Date(),
          resultCount,
        };

        // 移除重复的查询
        const filteredHistory = history.filter(h => h.query !== query.trim());
        
        // 添加新记录到开头
        const updatedHistory = [newRecord, ...filteredHistory];
        
        // 限制历史记录数量
        const limitedHistory = updatedHistory.slice(0, get().config.maxHistory);
        
        set({ history: limitedHistory });
      },

      // 清空历史
      clearHistory: () => {
        set({ history: [] });
      },

      // 从历史中移除
      removeFromHistory: (id: string) => {
        const history = get().history.filter(h => h.id !== id);
        set({ history });
      },

      // 记录搜索统计
      recordSearch: (query: string, resultCount: number) => {
        const stats = get().stats;
        const trimmedQuery = query.trim().toLowerCase();

        // 更新总搜索次数
        const totalSearches = stats.totalSearches + 1;

        // 更新最近查询
        const recentQueries = [trimmedQuery, ...stats.recentQueries.filter(q => q !== trimmedQuery)];
        const limitedRecent = recentQueries.slice(0, 10);

        // 更新热门查询
        const queryCount: Record<string, number> = {};
        limitedRecent.forEach(q => {
          queryCount[q] = (queryCount[q] || 0) + 1;
        });
        const popularQueries = Object.entries(queryCount)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 10)
          .map(([query]) => query);

        // 更新无结果查询
        let noResultQueries = [...stats.noResultQueries];
        if (resultCount === 0 && !noResultQueries.includes(trimmedQuery)) {
          noResultQueries = [trimmedQuery, ...noResultQueries].slice(0, 20);
        }

        set({
          stats: {
            totalSearches,
            popularQueries,
            recentQueries: limitedRecent,
            noResultQueries,
          }
        });
      },

      // 记录点击
      recordClick: (resultId: string, query: string) => {
        const history = get().history;
        const updatedHistory = history.map(h => 
          h.query === query ? { ...h, clickedResult: resultId } : h
        );
        set({ history: updatedHistory });
      },

      // 更新配置
      updateConfig: (newConfig: Partial<typeof DEFAULT_SEARCH_CONFIG>) => {
        const config = { ...get().config, ...newConfig };
        set({ config });
      },
    }),
    {
      name: 'search-storage',
      version: 1,
      // 只持久化部分数据
      partialize: (state) => ({
        history: state.history,
        stats: state.stats,
        config: state.config,
        filters: state.filters,
      }),
    }
  )
);
