'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import ShareButton from '@/components/share/ShareButton';

const ImageCompressorPage: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [compressedImage, setCompressedImage] = useState<string | null>(null);
  const [originalSize, setOriginalSize] = useState<number>(0);
  const [compressedSize, setCompressedSize] = useState<number>(0);
  const [quality, setQuality] = useState<number>(80);
  const [isCompressing, setIsCompressing] = useState<boolean>(false);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      setOriginalSize(file.size);
      setCompressedImage(null);
      setCompressedSize(0);
    }
  }, []);

  const compressImage = useCallback(async () => {
    if (!selectedFile) return;

    setIsCompressing(true);

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new window.Image();

      img.onload = () => {
        // 设置画布尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 绘制图片
        ctx?.drawImage(img, 0, 0);

        // 压缩并转换为blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              setCompressedSize(blob.size);
              const url = URL.createObjectURL(blob);
              setCompressedImage(url);
            }
            setIsCompressing(false);
          },
          'image/jpeg',
          quality / 100
        );
      };

      img.src = URL.createObjectURL(selectedFile);
    } catch (error) {
      console.error('压缩失败:', error);
      setIsCompressing(false);
    }
  }, [selectedFile, quality]);

  const downloadCompressed = useCallback(() => {
    if (compressedImage) {
      const link = document.createElement('a');
      link.href = compressedImage;
      link.download = `compressed_${selectedFile?.name || 'image.jpg'}`;
      link.click();
    }
  }, [compressedImage, selectedFile]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const compressionRatio = originalSize > 0 ? ((originalSize - compressedSize) / originalSize * 100).toFixed(1) : '0';

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">图片压缩工具</h1>
          <p className="text-gray-600">
            在线压缩图片，减小文件大小，支持JPG、PNG等格式
          </p>
        </div>

        <div className="space-y-6">
          {/* 文件选择 */}
          <Card>
            <CardHeader>
              <CardTitle>选择图片</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-input"
                />
                <label
                  htmlFor="file-input"
                  className="cursor-pointer flex flex-col items-center"
                >
                  <div className="text-4xl mb-4">📁</div>
                  <p className="text-lg font-medium text-gray-700 mb-2">
                    点击选择图片文件
                  </p>
                  <p className="text-sm text-gray-500">
                    支持 JPG、PNG、WebP 等格式
                  </p>
                </label>
              </div>

              {selectedFile && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <p className="font-medium">已选择文件:</p>
                  <p className="text-sm text-gray-600">
                    {selectedFile.name} ({formatFileSize(selectedFile.size)})
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 压缩设置 */}
          {selectedFile && (
            <Card>
              <CardHeader>
                <CardTitle>压缩设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      压缩质量: {quality}%
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(Number(e.target.value))}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>最小文件</span>
                      <span>最佳质量</span>
                    </div>
                  </div>

                  <Button
                    onClick={compressImage}
                    disabled={isCompressing}
                    className="w-full"
                  >
                    {isCompressing ? '压缩中...' : '开始压缩'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 压缩结果 */}
          {compressedImage && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>压缩结果</CardTitle>
                  {selectedFile && compressedImage && (
                    <ShareButton
                      toolId="image-compressor"
                      toolName="图片压缩工具"
                      input={`文件名: ${selectedFile.name}\n原始大小: ${formatFileSize(originalSize)}\n压缩质量: ${quality}%`}
                      output={`压缩后大小: ${formatFileSize(compressedSize)}\n压缩率: ${compressionRatio}%\n节省空间: ${formatFileSize(originalSize - compressedSize)}`}
                      options={{
                        fileName: selectedFile.name,
                        originalSize,
                        compressedSize,
                        quality,
                        compressionRatio: parseFloat(compressionRatio),
                        savedBytes: originalSize - compressedSize,
                      }}
                      size="sm"
                      showText={false}
                    />
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 原图预览 */}
                  <div>
                    <h3 className="font-medium mb-2">原图</h3>
                    <div className="relative w-full h-48 rounded-lg border overflow-hidden">
                      <Image
                        src={URL.createObjectURL(selectedFile!)}
                        alt="原图"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      大小: {formatFileSize(originalSize)}
                    </p>
                  </div>

                  {/* 压缩后预览 */}
                  <div>
                    <h3 className="font-medium mb-2">压缩后</h3>
                    <div className="relative w-full h-48 rounded-lg border overflow-hidden">
                      <Image
                        src={compressedImage}
                        alt="压缩后"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      大小: {formatFileSize(compressedSize)}
                    </p>
                  </div>
                </div>

                {/* 压缩统计 */}
                <div className="mt-6 p-4 bg-green-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-green-800">
                      压缩率: {compressionRatio}%
                    </span>
                    <span className="text-green-600">
                      节省: {formatFileSize(originalSize - compressedSize)}
                    </span>
                  </div>
                </div>

                {/* 下载按钮 */}
                <Button
                  onClick={downloadCompressed}
                  className="w-full mt-4"
                  variant="outline"
                >
                  📥 下载压缩后的图片
                </Button>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>支持格式:</strong> JPG、PNG、WebP、BMP等常见图片格式</p>
                <p><strong>压缩原理:</strong> 通过调整图片质量来减小文件大小</p>
                <p><strong>质量设置:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>10-30%: 最大压缩，适合缩略图</li>
                  <li>50-70%: 平衡压缩，适合网页使用</li>
                  <li>80-90%: 轻度压缩，保持较好质量</li>
                  <li>90-100%: 最小压缩，保持原始质量</li>
                </ul>
                <p><strong>注意事项:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>所有处理都在浏览器本地完成，不会上传到服务器</li>
                  <li>PNG格式会转换为JPG格式以获得更好的压缩效果</li>
                  <li>建议在压缩前备份原始图片</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ImageCompressorPage;
