/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

#main-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.header-nav {
    display: flex;
    gap: 15px;
}

.header-nav a {
    color: #3498db;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.header-nav a:hover {
    background-color: #e3f2fd;
}

/* Language Selector */
.language-selector {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.language-btn {
    background: #fff;
    border: 1px solid #ddd;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.language-btn:hover {
    border-color: #3498db;
}

.language-btn::after {
    content: '▼';
    font-size: 12px;
    color: #666;
}

.language-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.language-dropdown.show {
    display: block;
}

.language-option {
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #f0f0f0;
}

.language-option:hover {
    background-color: #f8f9fa;
}

.language-option:last-child {
    border-bottom: none;
}

/* Tip */
.tip {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 30px;
    text-align: center;
    font-size: 14px;
    color: #1976d2;
}

/* Current Timestamp Section */
.current-timestamp {
    margin-bottom: 40px;
}

.timestamp-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.timestamp-item {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.timestamp-item label {
    display: block;
    font-weight: 600;
    color: #555;
    margin-bottom: 10px;
    font-size: 14px;
}

.timestamp-value {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.timestamp-value span {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 16px;
    font-weight: 500;
    color: #2c3e50;
    flex: 1;
    word-break: break-all;
}

/* Copy Button */
.copy-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    position: relative;
    min-width: 40px;
}

.copy-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.copy-btn:active {
    transform: translateY(0);
}

/* Quick Copy Menu */
.quick-copy-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 100;
    display: none;
    min-width: 200px;
    padding: 10px;
}

.quick-copy-menu.show {
    display: block;
}

.quick-copy-btn {
    display: block;
    width: 100%;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
}

.quick-copy-btn:hover {
    background: #e9ecef;
    border-color: #3498db;
}

.custom-minutes {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e0e0e0;
}

.custom-minutes input {
    width: 60px;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
}

.custom-minutes span {
    font-size: 12px;
    color: #666;
}

.custom-copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.custom-copy-btn:hover {
    background: #218838;
}

/* Conversion Sections */
.conversion-section {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.conversion-section h2 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.conversion-section p {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.input-group input {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.convert-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.convert-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* Result Grid */
.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.result-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.result-item label {
    display: block;
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
    font-size: 13px;
}

.result-item span {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    color: #2c3e50;
    word-break: break-all;
}

/* About Section */
.about-section {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.about-section h2 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
}

.about-content {
    display: grid;
    gap: 20px;
}

.about-item h3 {
    font-size: 1.1rem;
    color: #34495e;
    margin-bottom: 10px;
    font-weight: 600;
}

.about-item p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0;
}

.about-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.about-item li {
    color: #666;
    line-height: 1.6;
    margin-bottom: 5px;
    padding-left: 15px;
    position: relative;
}

.about-item li::before {
    content: '•';
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 40px;
}

.footer p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Copy Success Animation */
.copy-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #28a745;
    color: white;
    padding: 15px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
}

.copy-success.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    #main-title {
        font-size: 2rem;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .timestamp-row {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
    }

    .result-grid {
        grid-template-columns: 1fr;
    }

    .tip {
        font-size: 13px;
    }

    .quick-copy-menu {
        right: auto;
        left: 0;
    }
}

@media (max-width: 480px) {
    .timestamp-value {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .copy-btn {
        align-self: flex-end;
        width: fit-content;
    }
}
