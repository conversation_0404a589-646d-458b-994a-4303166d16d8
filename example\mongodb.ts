import { MongoClient, ServerApiVersion, Db } from 'mongodb';
import dns from 'dns';

// 使用公共 DNS 服务器，避免本地路由器无法解析 TXT 记录导致 ETIMEOUT
dns.setServers(['8.8.8.8', '1.1.1.1']);

// 使用MONGO_URI环境变量简化MongoDB连接配置
console.log('尝试从环境变量加载MONGO_URI...');

// 直接使用确定的、已经正确编码的MongoDB URI
function constructMongoURI(): string {
  // 如果环境变量存在，使用环境变量
  if (process.env.MONGO_URI) {
    return process.env.MONGO_URI;
  }
  
  // 如果环境变量不存在，使用默认值
  console.log('环境变量MONGO_URI不存在，使用默认连接字符串');
  return "mongodb+srv://wuyoutao4147:jK9%24p2Lm%237xZ%40Qr5vB8*<EMAIL>/feedback_system";
}

// 生成正确编码的MongoDB URI
const mongoUri = constructMongoURI();

// 注意: constructMongoURI函数已经处理了密码编码，这里不需要重复处理
console.log('密码已在constructMongoURI函数中进行了编码');

console.log("process.env.MONGO_DB_USERNAME-", process.env.MONGO_URI)
console.log("process.env.MONGO_DB_USERNAME-", mongoUri)

// 检查是否使用环境变量
const usingEnvVar = process.env.MONGO_URI ? true : false;
console.log('是否使用环境变量:', usingEnvVar ? '是' : '否');

// 输出部分URI，避免泄露敏感信息
const maskedUri = mongoUri.replace(/(:.*@)/g, ':***@');
console.log('连接URI (已隐藏敏感信息):', maskedUri);

// 缓存变量以避免重复连接
let cachedClient: MongoClient | null = null;
let cachedDb: Db | null = null;

export async function connectToDatabase() {
  // 如果已经连接，直接返回缓存的客户端和数据库实例
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb };
  }

  try {
    // 创建新的MongoDB客户端连接
    const client = new MongoClient(mongoUri, {
      serverApi: ServerApiVersion.v1 // 使用最新版本的MongoDB API
    });

    // 连接到MongoDB
    await client.connect();
    
    // 验证连接
    await client.db('admin').command({ ping: 1 });
    console.log('✅ Successfully connected to MongoDB Atlas');

    // 从URI中提取数据库名称，或使用默认值
    const dbName = mongoUri.split('/').pop() || "feedback_system";
    const db = client.db(dbName);

    // 缓存连接和数据库实例
    cachedClient = client;
    cachedDb = db;

    return { client, db };
  } catch (error) {
    console.error('❗ MongoDB连接错误:', error);
    throw error;
  }
}

// 获取集合的辅助函数
export async function getCollection(collectionName: string) {
  const { db } = await connectToDatabase();
  return db.collection(collectionName);
}
