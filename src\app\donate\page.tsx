import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '支持我们 - Tool List',
  description: '如果Tool List对您有帮助，欢迎通过支付宝或微信支持我们的开发工作。您的支持是我们持续改进的动力。',
  keywords: ['捐赠', '支持', '赞助', 'Tool List', '开发者工具'],
  openGraph: {
    title: '支持我们 - Tool List',
    description: '如果Tool List对您有帮助，欢迎通过支付宝或微信支持我们的开发工作。',
    url: 'https://cypress.fun/donate',
  },
  twitter: {
    card: 'summary_large_image',
    title: '支持我们 - Tool List',
    description: '如果Tool List对您有帮助，欢迎通过支付宝或微信支持我们的开发工作。',
  },
  alternates: {
    canonical: 'https://cypress.fun/donate',
  },
};

export default function DonatePage() {
  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <span className="text-gray-900">支持我们</span>
        </nav>

        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">💝 支持我们</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            如果 Tool List 对您的工作有所帮助，欢迎通过小额捐赠支持我们的持续开发
          </p>
        </div>
      </div>

      {/* 网站价值介绍 */}
      <div className="space-y-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span className="text-2xl">🌟</span>
              <span>Tool List 的价值</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <span className="text-xl">🚀</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">提升开发效率</h3>
                    <p className="text-gray-600 text-sm">集成11个常用开发工具，一站式解决日常开发需求</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-xl">🔒</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">隐私安全</h3>
                    <p className="text-gray-600 text-sm">所有数据本地处理，不上传服务器，保护您的隐私</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-xl">📱</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">跨平台支持</h3>
                    <p className="text-gray-600 text-sm">响应式设计，支持桌面端和移动端使用</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <span className="text-xl">⚡</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">快速便捷</h3>
                    <p className="text-gray-600 text-sm">无需安装，打开即用，节省您的宝贵时间</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-xl">🆓</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">完全免费</h3>
                    <p className="text-gray-600 text-sm">所有工具永久免费使用，无任何限制</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-xl">🔄</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">持续更新</h3>
                    <p className="text-gray-600 text-sm">定期添加新工具，优化用户体验</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 支持方式 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span className="text-2xl">💰</span>
              <span>支持方式</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-8">
              {/* 支付宝 */}
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-center space-x-2">
                  <span className="text-2xl">💙</span>
                  <span>支付宝</span>
                </h3>
                <div className="bg-white p-4 rounded-lg border-2 border-gray-200 inline-block">
                  <Image
                    src="/images/sweepzfb.jpg"
                    alt="支付宝收款码"
                    width={200}
                    height={200}
                    className="rounded-lg"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-3">
                  使用支付宝扫描上方二维码
                </p>
              </div>

              {/* 微信 */}
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-center space-x-2">
                  <span className="text-2xl">💚</span>
                  <span>微信支付</span>
                </h3>
                <div className="bg-white p-4 rounded-lg border-2 border-gray-200 inline-block">
                  <Image
                    src="/images/sweepwx.png"
                    alt="微信收款码"
                    width={200}
                    height={200}
                    className="rounded-lg"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-3">
                  使用微信扫描上方二维码
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 感谢语 */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="space-y-4">
              <div className="text-4xl">🙏</div>
              <h2 className="text-2xl font-bold text-gray-900">感谢您的支持</h2>
              <div className="max-w-2xl mx-auto space-y-3 text-gray-600">
                <p>
                  您的每一份支持都是我们前进的动力。无论金额大小，都代表着您对 Tool List 的认可和鼓励。
                </p>
                <p>
                  我们承诺将继续：
                </p>
                <ul className="text-left space-y-2 max-w-md mx-auto">
                  <li>• 保持所有工具完全免费</li>
                  <li>• 定期添加新的实用工具</li>
                  <li>• 持续优化用户体验</li>
                  <li>• 确保网站稳定运行</li>
                </ul>
                <p className="text-sm text-gray-500 mt-6">
                  如果您有任何建议或想法，欢迎通过 
                  <Link href="/contact" className="text-blue-600 hover:text-blue-800 mx-1">
                    联系我们
                  </Link>
                  页面与我们交流。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 其他支持方式 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span className="text-2xl">🤝</span>
              <span>其他支持方式</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl mb-2">📢</div>
                <h3 className="font-semibold text-gray-900 mb-2">分享推荐</h3>
                <p className="text-sm text-gray-600">
                  向朋友和同事推荐 Tool List
                </p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl mb-2">💬</div>
                <h3 className="font-semibold text-gray-900 mb-2">意见反馈</h3>
                <p className="text-sm text-gray-600">
                  <Link href="/feedback" className="text-blue-600 hover:text-blue-800">
                    提供宝贵建议
                  </Link>
                </p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl mb-2">⭐</div>
                <h3 className="font-semibold text-gray-900 mb-2">使用体验</h3>
                <p className="text-sm text-gray-600">
                  <Link href="/tools" className="text-blue-600 hover:text-blue-800">
                    体验更多工具
                  </Link>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
