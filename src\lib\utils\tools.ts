// 工具相关的工具函数

import {
  TimestampConverterState,
  JsonFormatterState,
  TextConverterState,
  IpConverterState,
  ToolOperationResult
} from '@/types/tools';

// 时间戳转换工具函数
export class TimestampConverter {
  static convert(state: TimestampConverterState): ToolOperationResult<string> {
    try {
      const { input, inputType, outputFormat, customFormat } = state;

      if (!input.trim()) {
        return { success: false, error: '请输入要转换的内容' };
      }

      let date: Date;

      if (inputType === 'timestamp') {
        // 输入是时间戳
        const timestamp = parseInt(input.trim());
        if (isNaN(timestamp)) {
          return { success: false, error: '无效的时间戳格式' };
        }

        // 判断是秒还是毫秒
        const timestampLength = timestamp.toString().length;
        date = new Date(timestampLength === 10 ? timestamp * 1000 : timestamp);
      } else {
        // 输入是日期时间字符串
        date = new Date(input.trim());
        if (isNaN(date.getTime())) {
          return { success: false, error: '无效的日期时间格式' };
        }
      }

      let result: string;

      switch (outputFormat) {
        case 'iso':
          result = date.toISOString();
          break;
        case 'local':
          result = date.toLocaleString();
          break;
        case 'utc':
          result = date.toUTCString();
          break;
        case 'custom':
          if (customFormat) {
            result = this.formatDate(date, customFormat);
          } else {
            result = date.toString();
          }
          break;
        default:
          result = date.toString();
      }

      return {
        success: true,
        data: result,
        metadata: {
          timestamp: Math.floor(date.getTime() / 1000),
          milliseconds: date.getTime(),
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '转换失败'
      };
    }
  }

  private static formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace(/YYYY/g, year.toString())
      .replace(/MM/g, month)
      .replace(/DD/g, day)
      .replace(/HH/g, hours)
      .replace(/mm/g, minutes)
      .replace(/ss/g, seconds);
  }
}

// JSON 格式化工具函数
export class JsonFormatter {
  static format(state: JsonFormatterState): ToolOperationResult<string> {
    try {
      const { input, indent, sortKeys, validateOnly } = state;

      if (!input.trim()) {
        return { success: false, error: '请输入JSON内容' };
      }

      // 解析JSON
      const parsed = JSON.parse(input);

      if (validateOnly) {
        return {
          success: true,
          data: '✅ JSON格式正确',
          metadata: { valid: true }
        };
      }

      // 格式化选项
      const replacer = sortKeys ? this.createSortedReplacer() : undefined;
      const formatted = JSON.stringify(parsed, replacer, indent);

      return {
        success: true,
        data: formatted,
        metadata: {
          size: formatted.length,
          lines: formatted.split('\n').length,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON格式错误'
      };
    }
  }

  static minify(input: string): ToolOperationResult<string> {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入JSON内容' };
      }

      const parsed = JSON.parse(input);
      const minified = JSON.stringify(parsed);

      return {
        success: true,
        data: minified,
        metadata: {
          originalSize: input.length,
          minifiedSize: minified.length,
          compressionRatio: ((input.length - minified.length) / input.length * 100).toFixed(2) + '%',
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON格式错误'
      };
    }
  }

  private static createSortedReplacer() {
    return function(key: string, value: unknown) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        const sorted: Record<string, unknown> = {};
        const obj = value as Record<string, unknown>;
        Object.keys(obj).sort().forEach(k => {
          sorted[k] = obj[k];
        });
        return sorted;
      }
      return value;
    };
  }
}

// 文本转换工具函数
export class TextConverter {
  static convert(state: TextConverterState): ToolOperationResult<string> {
    try {
      const { input, operation } = state;

      if (!input) {
        return { success: false, error: '请输入要转换的文本' };
      }

      let result: string;

      switch (operation) {
        case 'uppercase':
          result = input.toUpperCase();
          break;
        case 'lowercase':
          result = input.toLowerCase();
          break;
        case 'capitalize':
          result = input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
          break;
        case 'camelCase':
          result = this.toCamelCase(input);
          break;
        case 'snakeCase':
          result = this.toSnakeCase(input);
          break;
        case 'kebabCase':
          result = this.toKebabCase(input);
          break;
        case 'base64Encode':
          result = btoa(unescape(encodeURIComponent(input)));
          break;
        case 'base64Decode':
          result = decodeURIComponent(escape(atob(input)));
          break;
        case 'urlEncode':
          result = encodeURIComponent(input);
          break;
        case 'urlDecode':
          result = decodeURIComponent(input);
          break;
        case 'htmlEncode':
          result = this.htmlEncode(input);
          break;
        case 'htmlDecode':
          result = this.htmlDecode(input);
          break;
        default:
          return { success: false, error: '不支持的转换操作' };
      }

      return {
        success: true,
        data: result,
        metadata: {
          originalLength: input.length,
          resultLength: result.length,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '转换失败'
      };
    }
  }

  private static toCamelCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  private static toSnakeCase(str: string): string {
    return str.replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('_');
  }

  private static toKebabCase(str: string): string {
    return str.replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('-');
  }

  private static htmlEncode(str: string): string {
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  }

  private static htmlDecode(str: string): string {
    const div = document.createElement('div');
    div.innerHTML = str;
    return div.textContent || div.innerText || '';
  }
}

// IP地址转换工具函数
export class IpConverter {
  static convert(state: IpConverterState): ToolOperationResult<string> {
    try {
      const { input, inputType, outputType } = state;

      if (!input.trim()) {
        return { success: false, error: '请输入IP地址' };
      }

      // 先转换为标准IPv4格式
      let ipv4: string;

      switch (inputType) {
        case 'ipv4':
          ipv4 = input.trim();
          if (!this.isValidIPv4(ipv4)) {
            return { success: false, error: '无效的IPv4地址格式' };
          }
          break;
        case 'decimal':
          ipv4 = this.decimalToIPv4(parseInt(input.trim()));
          break;
        case 'hex':
          ipv4 = this.hexToIPv4(input.trim());
          break;
        case 'binary':
          ipv4 = this.binaryToIPv4(input.trim());
          break;
        default:
          return { success: false, error: '不支持的输入格式' };
      }

      // 转换为目标格式
      let result: string;

      switch (outputType) {
        case 'ipv4':
          result = ipv4;
          break;
        case 'decimal':
          result = this.ipv4ToDecimal(ipv4).toString();
          break;
        case 'hex':
          result = this.ipv4ToHex(ipv4);
          break;
        case 'binary':
          result = this.ipv4ToBinary(ipv4);
          break;
        default:
          return { success: false, error: '不支持的输出格式' };
      }

      return {
        success: true,
        data: result,
        metadata: {
          ipv4,
          decimal: this.ipv4ToDecimal(ipv4),
          hex: this.ipv4ToHex(ipv4),
          binary: this.ipv4ToBinary(ipv4),
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '转换失败'
      };
    }
  }

  private static isValidIPv4(ip: string): boolean {
    const parts = ip.split('.');
    if (parts.length !== 4) return false;

    return parts.every(part => {
      const num = parseInt(part);
      return !isNaN(num) && num >= 0 && num <= 255;
    });
  }

  private static ipv4ToDecimal(ip: string): number {
    const parts = ip.split('.').map(part => parseInt(part));
    return (parts[0] << 24) + (parts[1] << 16) + (parts[2] << 8) + parts[3];
  }

  private static decimalToIPv4(decimal: number): string {
    return [
      (decimal >>> 24) & 255,
      (decimal >>> 16) & 255,
      (decimal >>> 8) & 255,
      decimal & 255
    ].join('.');
  }

  private static ipv4ToHex(ip: string): string {
    const parts = ip.split('.').map(part => parseInt(part));
    return parts.map(part => part.toString(16).padStart(2, '0')).join('').toUpperCase();
  }

  private static hexToIPv4(hex: string): string {
    const clean = hex.replace(/[^0-9A-Fa-f]/g, '');
    if (clean.length !== 8) throw new Error('无效的十六进制格式');

    const parts = [];
    for (let i = 0; i < 8; i += 2) {
      parts.push(parseInt(clean.substr(i, 2), 16));
    }
    return parts.join('.');
  }

  private static ipv4ToBinary(ip: string): string {
    const parts = ip.split('.').map(part => parseInt(part));
    return parts.map(part => part.toString(2).padStart(8, '0')).join('.');
  }

  private static binaryToIPv4(binary: string): string {
    const clean = binary.replace(/[^01]/g, '');
    if (clean.length !== 32) throw new Error('无效的二进制格式');

    const parts = [];
    for (let i = 0; i < 32; i += 8) {
      parts.push(parseInt(clean.substr(i, 8), 2));
    }
    return parts.join('.');
  }
}
