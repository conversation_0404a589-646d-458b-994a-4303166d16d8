import { Metadata } from 'next';
import ColorConverterClient from './ColorConverterClient';

export const metadata: Metadata = {
  title: '颜色转换器 - HEX RGB HSL颜色格式转换 | Tool List',
  description: '专业的颜色格式转换工具，支持HEX、RGB、HSL、HSV、CMYK等格式互转。提供颜色选择器、预设色彩和自定义输入，设计师和开发者的必备工具。',
  keywords: [
    '颜色转换', '颜色格式转换', 'HEX转RGB', 'RGB转HEX', 'HSL转换',
    '颜色选择器', '色彩工具', 'CMYK转换', 'HSV转换', 'color converter',
    '设计工具', '前端工具', '网页设计', '色彩搭配', '开发者工具'
  ],
  openGraph: {
    title: '颜色转换器 - 免费在线颜色格式转换工具',
    description: '支持HEX、RGB、HSL、HSV、CMYK等格式互转，设计师必备工具',
    url: 'https://cypress.fun/tools/color-converter',
    siteName: 'Tool List',
    images: [
      {
        url: 'https://cypress.fun/og-color-converter.png',
        width: 1200,
        height: 630,
        alt: '颜色转换器',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '颜色转换器 - Tool List',
    description: '专业的颜色格式转换工具，支持多种颜色格式互转',
    images: ['https://cypress.fun/og-color-converter.png'],
  },
  alternates: {
    canonical: 'https://cypress.fun/tools/color-converter',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function ColorConverterPage() {
  return <ColorConverterClient />;
}


