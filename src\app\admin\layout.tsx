import React from 'react';
import Link from 'next/link';
import {
  Database,
  Wrench,
  BarChart3,
  Settings,
  Home,
  Users,
  MessageSquare
} from 'lucide-react';
import DatabaseStatus from '@/components/admin/DatabaseStatus';

const adminNavItems = [
  {
    href: '/admin',
    label: '概览',
    icon: Home,
  },
  {
    href: '/admin/database',
    label: '数据库管理',
    icon: Database,
  },
  {
    href: '/admin/tools',
    label: '工具管理',
    icon: Wrench,
  },
  {
    href: '/admin/analytics',
    label: '数据分析',
    icon: BarChart3,
  },
  {
    href: '/admin/users',
    label: '用户管理',
    icon: Users,
  },
  {
    href: '/admin/feedback',
    label: '反馈管理',
    icon: MessageSquare,
  },
  {
    href: '/admin/settings',
    label: '系统设置',
    icon: Settings,
  },
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link href="/" className="text-xl font-bold text-blue-600">
                Tool List
              </Link>
              <span className="text-gray-400">|</span>
              <span className="text-gray-600">管理后台</span>
            </div>
            
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                返回网站
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* 数据库状态 */}
        <div className="mb-6">
          <DatabaseStatus />
        </div>

        {/* 导航菜单 */}
        <div className="flex flex-wrap gap-2 mb-6">
          {adminNavItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.href}
                href={item.href}
                className="flex items-center gap-2 px-4 py-2 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors"
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{item.label}</span>
              </Link>
            );
          })}
        </div>

        {/* 页面内容 */}
        <main>{children}</main>
      </div>
    </div>
  );
}
