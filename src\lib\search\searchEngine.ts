import { SearchResult, SearchQuery, SearchFilters, SEARCH_SHORTCUTS } from '@/types/search';
import { TOOLS } from '@/lib/constants/tools';

// 搜索数据源
const getSearchableData = (): SearchResult[] => {
  const results: SearchResult[] = [];

  // 添加工具数据
  TOOLS.forEach(tool => {
    results.push({
      id: tool.id,
      type: 'tool',
      title: tool.name,
      description: tool.description,
      url: tool.path,
      icon: tool.icon,
      category: tool.category,
      tags: tool.tags,
      relevanceScore: 0,
      lastUpdated: tool.updatedAt,
    });
  });

  // 添加页面数据
  const pages = [
    {
      id: 'tools',
      title: '工具列表',
      description: '浏览所有可用的开发工具',
      url: '/tools',
      icon: '🔧',
      category: 'navigation',
      tags: ['工具', '列表', '浏览'],
    },
    {
      id: 'navigation',
      title: '网站导航',
      description: '精选优质网站收藏',
      url: '/navigation',
      icon: '🌐',
      category: 'navigation',
      tags: ['网站', '导航', '收藏'],
    },
    {
      id: 'profile',
      title: '个人中心',
      description: '管理个人信息和偏好设置',
      url: '/profile',
      icon: '👤',
      category: 'user',
      tags: ['个人', '设置', '偏好'],
    },
    {
      id: 'history',
      title: '使用历史',
      description: '查看工具使用记录',
      url: '/history',
      icon: '📝',
      category: 'user',
      tags: ['历史', '记录', '统计'],
    },
    {
      id: 'feedback',
      title: '意见反馈',
      description: '提交问题和建议',
      url: '/feedback',
      icon: '💬',
      category: 'support',
      tags: ['反馈', '建议', '问题'],
    },
    {
      id: 'about',
      title: '关于我们',
      description: '了解项目信息',
      url: '/about',
      icon: 'ℹ️',
      category: 'info',
      tags: ['关于', '信息', '项目'],
    },
  ];

  pages.forEach(page => {
    results.push({
      ...page,
      type: 'page',
      relevanceScore: 0,
    });
  });

  // 添加帮助内容
  const helpContent = [
    {
      id: 'how-to-use-timestamp',
      title: '如何使用时间戳转换工具',
      description: '学习如何在不同时间格式之间转换',
      url: '/tools/timestamp-converter',
      category: 'tutorial',
      tags: ['教程', '时间戳', '转换'],
    },
    {
      id: 'json-formatting-guide',
      title: 'JSON格式化指南',
      description: '了解JSON格式化和验证的最佳实践',
      url: '/tools/json-formatter',
      category: 'tutorial',
      tags: ['教程', 'JSON', '格式化'],
    },
    {
      id: 'color-theory',
      title: '颜色理论基础',
      description: '了解不同颜色格式的区别和用途',
      url: '/tools/color-converter',
      category: 'tutorial',
      tags: ['教程', '颜色', '理论'],
    },
  ];

  helpContent.forEach(help => {
    results.push({
      ...help,
      type: 'help',
      icon: '❓',
      relevanceScore: 0,
    });
  });

  return results;
};

// 计算相关性评分
const calculateRelevanceScore = (item: SearchResult, query: string): number => {
  const normalizedQuery = query.toLowerCase().trim();
  const title = item.title.toLowerCase();
  const description = item.description.toLowerCase();
  const tags = item.tags?.join(' ').toLowerCase() || '';
  
  let score = 0;

  // 标题完全匹配
  if (title === normalizedQuery) {
    score += 100;
  }
  // 标题开头匹配
  else if (title.startsWith(normalizedQuery)) {
    score += 80;
  }
  // 标题包含
  else if (title.includes(normalizedQuery)) {
    score += 60;
  }

  // 描述匹配
  if (description.includes(normalizedQuery)) {
    score += 30;
  }

  // 标签匹配
  if (tags.includes(normalizedQuery)) {
    score += 40;
  }

  // 模糊匹配（简单实现）
  const queryWords = normalizedQuery.split(' ');
  queryWords.forEach(word => {
    if (word.length > 2) {
      if (title.includes(word)) score += 20;
      if (description.includes(word)) score += 10;
      if (tags.includes(word)) score += 15;
    }
  });

  // 类型权重
  switch (item.type) {
    case 'tool':
      score *= 1.2; // 工具优先级最高
      break;
    case 'page':
      score *= 1.0;
      break;
    case 'help':
      score *= 0.8;
      break;
    case 'website':
      score *= 0.9;
      break;
  }

  return Math.min(score, 100);
};

// 检查快捷方式
const checkShortcuts = (query: string): SearchResult | null => {
  const normalizedQuery = query.toLowerCase().trim();
  
  for (const shortcut of SEARCH_SHORTCUTS) {
    if (shortcut.aliases.some(alias => 
      alias.toLowerCase() === normalizedQuery || 
      alias.toLowerCase().includes(normalizedQuery)
    )) {
      const tool = TOOLS.find(t => t.path === shortcut.target);
      if (tool) {
        return {
          id: `shortcut-${tool.id}`,
          type: 'tool',
          title: tool.name,
          description: `快捷访问: ${tool.description}`,
          url: tool.path,
          icon: tool.icon,
          category: tool.category,
          tags: tool.tags,
          relevanceScore: 95,
          lastUpdated: tool.updatedAt,
        };
      }
    }
  }
  
  return null;
};

// 主搜索函数
export const searchItems = (query: SearchQuery, filters?: SearchFilters): SearchResult[] => {
  if (!query.query || query.query.trim().length === 0) {
    return [];
  }

  const allItems = getSearchableData();
  let results: SearchResult[] = [];

  // 检查快捷方式
  const shortcutResult = checkShortcuts(query.query);
  if (shortcutResult) {
    results.push(shortcutResult);
  }

  // 计算相关性并过滤
  allItems.forEach(item => {
    const score = calculateRelevanceScore(item, query.query);
    if (score > 0) {
      results.push({
        ...item,
        relevanceScore: score,
      });
    }
  });

  // 应用过滤器
  if (filters) {
    if (filters.type !== 'all') {
      results = results.filter(item => item.type === filters.type);
    }
    if (filters.category !== 'all') {
      results = results.filter(item => item.category === filters.category);
    }
  }

  // 排序
  const sortBy = filters?.sortBy || 'relevance';
  const sortOrder = filters?.sortOrder || 'desc';

  results.sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'relevance':
        comparison = a.relevanceScore - b.relevanceScore;
        break;
      case 'name':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'category':
        comparison = (a.category || '').localeCompare(b.category || '');
        break;
      case 'recent':
        const aDate = a.lastUpdated || new Date(0);
        const bDate = b.lastUpdated || new Date(0);
        comparison = aDate.getTime() - bDate.getTime();
        break;
    }

    return sortOrder === 'desc' ? -comparison : comparison;
  });

  // 应用限制
  const limit = query.limit || 20;
  const offset = query.offset || 0;
  
  return results.slice(offset, offset + limit);
};

// 获取搜索建议
export const getSearchSuggestions = (query: string): string[] => {
  if (!query || query.length < 2) {
    return [];
  }

  const suggestions: string[] = [];
  const normalizedQuery = query.toLowerCase();

  // 从工具名称和标签中获取建议
  TOOLS.forEach(tool => {
    if (tool.name.toLowerCase().includes(normalizedQuery)) {
      suggestions.push(tool.name);
    }
    tool.tags.forEach(tag => {
      if (tag.toLowerCase().includes(normalizedQuery) && !suggestions.includes(tag)) {
        suggestions.push(tag);
      }
    });
  });

  // 从快捷方式中获取建议
  SEARCH_SHORTCUTS.forEach(shortcut => {
    shortcut.aliases.forEach(alias => {
      if (alias.toLowerCase().includes(normalizedQuery) && !suggestions.includes(alias)) {
        suggestions.push(alias);
      }
    });
  });

  return suggestions.slice(0, 8);
};
