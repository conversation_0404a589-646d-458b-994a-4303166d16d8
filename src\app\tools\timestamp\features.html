<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Features - Unix Timestamp Converter</title>
    <meta name="description" content="Discover the powerful features of our Unix timestamp converter tool including smart copy system, quick time shortcuts, and multi-language support.">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⏰</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header>
            <div class="header-content">
                <h1>Features</h1>
                <div class="header-nav">
                    <a href="index.html">🏠 Home</a>
                    <a href="case-converter.html">🔤 Case Converter</a>
                </div>
            </div>
        </header>

        <!-- Features Content -->
        <section class="features-section">
            <div class="feature-item">
                <h2>📋 Smart Copy System</h2>
                <ul>
                    <li><strong>Icon-based copy buttons:</strong> Intuitive 📋 icons instead of text</li>
                    <li><strong>Double-click shortcuts:</strong> Both PC and mobile support double-click for quick copy menu</li>
                    <li><strong>Green success feedback:</strong> Visual ✓ animation when copy succeeds</li>
                    <li><strong>Anti-duplicate triggers:</strong> Optimized double-click logic prevents accidental copying</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>🎯 Quick Time Copy</h2>
                <ul>
                    <li><strong>Preset time intervals:</strong> 1 minute, 3 minutes, 5 minutes future timestamps</li>
                    <li><strong>Custom time input:</strong> Enter any number of minutes, automatically cached user preferences</li>
                    <li><strong>Real-time calculation:</strong> Dynamic calculation of future timestamps, supports both seconds and milliseconds</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>🌍 Multi-language Support</h2>
                <ul>
                    <li><strong>10 languages:</strong> English, Chinese, Hindi, Japanese, German, British English, Russian, Korean, Canadian English, French</li>
                    <li><strong>Smart tips:</strong> PC shows "Double-click copy buttons for quick copy", mobile also supports double-click</li>
                    <li><strong>URL path support:</strong> Multi-language URL path access (like /zh-CN/, /ja-JP/)</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>📱 Mobile Optimization</h2>
                <ul>
                    <li><strong>Touch-friendly:</strong> Optimized mobile touch experience with double-click support</li>
                    <li><strong>Responsive design:</strong> Perfect adaptation to various screen sizes</li>
                    <li><strong>Huawei browser compatibility:</strong> Special optimization for Huawei phone browser copy functionality</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>🔧 Technical Features</h2>
                <ul>
                    <li><strong>Multi-layer copy fallback:</strong> Modern Clipboard API → execCommand → Manual copy dialog</li>
                    <li><strong>Smart format detection:</strong> Automatically recognizes seconds, milliseconds, microseconds, nanoseconds timestamp formats</li>
                    <li><strong>Real-time conversion:</strong> Convert as you type, no button clicking required</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>⚡ Performance & Reliability</h2>
                <ul>
                    <li><strong>Lightweight:</strong> Pure HTML/CSS/JavaScript, no external dependencies</li>
                    <li><strong>Fast loading:</strong> Optimized for quick page load times</li>
                    <li><strong>Cross-browser:</strong> Works on all modern browsers</li>
                    <li><strong>Offline capable:</strong> Core functionality works without internet connection</li>
                </ul>
            </div>

            <div class="feature-item">
                <h2>🎨 User Experience</h2>
                <ul>
                    <li><strong>Clean interface:</strong> Minimalist design focused on functionality</li>
                    <li><strong>Intuitive navigation:</strong> Easy to understand and use</li>
                    <li><strong>Accessibility:</strong> Keyboard navigation and screen reader friendly</li>
                    <li><strong>Visual feedback:</strong> Clear indicators for all user actions</li>
                </ul>
            </div>
        </section>

        <!-- Back to Home -->
        <div class="back-home">
            <a href="index.html" class="back-btn">← Back to Converter</a>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.</p>
        </footer>
    </div>
</body>
</html>
