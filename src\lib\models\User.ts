import mongoose, { Document, Schema } from 'mongoose';

// 用户接口
export interface IUser extends Document {
  _id: string;
  email: string;
  name: string;
  password?: string;
  avatar?: string;
  role: 'user' | 'admin';
  isEmailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLoginAt?: Date;
  loginCount: number;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      browser: boolean;
    };
  };
  favoriteTools: string[];
  recentTools: Array<{
    toolId: string;
    lastUsed: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// 用户 Schema
const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: [true, '邮箱是必需的'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址'],
  },
  name: {
    type: String,
    required: [true, '姓名是必需的'],
    trim: true,
    minlength: [2, '姓名至少需要2个字符'],
    maxlength: [50, '姓名不能超过50个字符'],
  },
  password: {
    type: String,
    minlength: [6, '密码至少需要6个字符'],
    select: false, // 默认不返回密码字段
  },
  avatar: {
    type: String,
    default: null,
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user',
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  emailVerificationToken: {
    type: String,
    select: false,
  },
  passwordResetToken: {
    type: String,
    select: false,
  },
  passwordResetExpires: {
    type: Date,
    select: false,
  },
  lastLoginAt: {
    type: Date,
    default: null,
  },
  loginCount: {
    type: Number,
    default: 0,
  },
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system',
    },
    language: {
      type: String,
      default: 'zh-CN',
    },
    timezone: {
      type: String,
      default: 'Asia/Shanghai',
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      browser: {
        type: Boolean,
        default: true,
      },
    },
  },
  favoriteTools: [{
    type: String,
    ref: 'Tool',
  }],
  recentTools: [{
    toolId: {
      type: String,
      ref: 'Tool',
      required: true,
    },
    lastUsed: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      delete ret.passwordResetExpires;
      return ret;
    },
  },
});

// 索引
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ createdAt: 1 });
UserSchema.index({ lastLoginAt: 1 });
UserSchema.index({ 'recentTools.toolId': 1 });
UserSchema.index({ favoriteTools: 1 });

// 中间件：保存前处理
UserSchema.pre('save', function(next) {
  // 限制最近使用的工具数量
  if (this.recentTools && this.recentTools.length > 20) {
    this.recentTools = this.recentTools
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())
      .slice(0, 20);
  }

  next();
});

// 实例方法：添加最近使用的工具
UserSchema.methods.addRecentTool = function(toolId: string) {
  // 移除已存在的记录
  this.recentTools = this.recentTools.filter(
    (item: { toolId: string }) => item.toolId !== toolId
  );

  // 添加到开头
  this.recentTools.unshift({
    toolId,
    lastUsed: new Date(),
  });

  // 限制数量
  if (this.recentTools.length > 20) {
    this.recentTools = this.recentTools.slice(0, 20);
  }

  return this.save();
};

// 实例方法：切换收藏工具
UserSchema.methods.toggleFavoriteTool = function(toolId: string) {
  const index = this.favoriteTools.indexOf(toolId);

  if (index > -1) {
    // 移除收藏
    this.favoriteTools.splice(index, 1);
  } else {
    // 添加收藏
    this.favoriteTools.push(toolId);
  }

  return this.save();
};

// 静态方法：根据邮箱查找用户
UserSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

// 静态方法：创建用户
UserSchema.statics.createUser = function(userData: Partial<IUser>) {
  return this.create({
    ...userData,
    email: userData.email?.toLowerCase(),
  });
};

// 虚拟字段：用户显示名称
UserSchema.virtual('displayName').get(function() {
  return this.name || this.email.split('@')[0];
});

// 确保虚拟字段被序列化
UserSchema.set('toJSON', { virtuals: true });
UserSchema.set('toObject', { virtuals: true });

// 导出模型
const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
