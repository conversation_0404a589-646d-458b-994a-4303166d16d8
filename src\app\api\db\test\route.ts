import { NextRequest, NextResponse } from 'next/server';
import connectDB, { getConnectionStatus } from '@/lib/db/mongodb';

export async function GET() {
  try {
    // 检查是否有数据库连接配置
    if (!process.env.MONGODB_URI) {
      return NextResponse.json({
        success: false,
        message: '数据库未配置',
        error: '请在环境变量中配置 MONGODB_URI',
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    // 尝试连接数据库
    const startTime = Date.now();
    await connectDB();
    const connectionTime = Date.now() - startTime;

    // 获取连接状态
    const status = getConnectionStatus();

    return NextResponse.json({
      success: true,
      message: 'MongoDB 连接测试成功',
      data: {
        status,
        connectionTime: `${connectionTime}ms`,
        timestamp: new Date().toISOString(),
        database: process.env.MONGODB_DB || 'toollist',
        uri: process.env.MONGODB_URI?.replace(/\/\/.*@/, '//***:***@') || 'Not configured', // 隐藏密码
      }
    });
  } catch (error) {
    console.error('数据库连接测试失败:', error);

    return NextResponse.json({
      success: false,
      message: 'MongoDB 连接测试失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查是否有数据库连接配置
    if (!process.env.MONGODB_URI) {
      return NextResponse.json({
        success: false,
        message: '数据库未配置',
        error: '请在环境变量中配置 MONGODB_URI',
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'ping') {
      // 执行 ping 测试
      const mongoose = await connectDB();
      const admin = mongoose.connection.db?.admin();

      if (!admin) {
        throw new Error('无法获取数据库管理接口');
      }

      const startTime = Date.now();
      const pingResult = await admin.ping();
      const pingTime = Date.now() - startTime;

      return NextResponse.json({
        success: true,
        message: 'Ping 测试成功',
        data: {
          ping: pingResult,
          pingTime: `${pingTime}ms`,
          timestamp: new Date().toISOString(),
        }
      });
    }

    if (action === 'stats') {
      // 获取数据库统计信息
      const mongoose = await connectDB();
      const db = mongoose.connection.db;

      if (!db) {
        throw new Error('数据库连接未建立');
      }

      const stats = await db.stats();
      const collections = await db.listCollections().toArray();

      return NextResponse.json({
        success: true,
        message: '数据库统计信息获取成功',
        data: {
          stats: {
            collections: stats.collections,
            dataSize: stats.dataSize,
            storageSize: stats.storageSize,
            indexes: stats.indexes,
            objects: stats.objects,
          },
          collections: collections.map(col => ({
            name: col.name,
            type: col.type,
          })),
          timestamp: new Date().toISOString(),
        }
      });
    }

    return NextResponse.json({
      success: false,
      message: '不支持的操作',
      error: `未知的 action: ${action}`,
    }, { status: 400 });

  } catch (error) {
    console.error('数据库操作失败:', error);

    return NextResponse.json({
      success: false,
      message: '数据库操作失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
