'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, Button, CopyToast } from '@/components/ui';
import ShareButton from '@/components/share/ShareButton';
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard';

export default function CaseConverterPage() {
  const [textInput, setTextInput] = useState('');
  const [result, setResult] = useState('');
  const [currentCaseType, setCurrentCaseType] = useState('');
  const { copyState, copyToClipboard, hideCopyToast } = useCopyToClipboard();

  const convertCase = (type: string) => {
    if (!textInput.trim()) {
      setResult('');
      setCurrentCaseType('');
      return;
    }

    let convertedText = '';

    switch (type) {
      case 'upper':
        convertedText = textInput.toUpperCase();
        break;
      case 'lower':
        convertedText = textInput.toLowerCase();
        break;
      case 'title':
        convertedText = textInput.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
        break;
      case 'camel':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase());
        break;
      case 'pascal':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase())
          .replace(/^./, str => str.toUpperCase());
        break;
      case 'snake':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+/g, '_')
          .replace(/^_+|_+$/g, '');
        break;
      case 'kebab':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
        break;
      default:
        convertedText = textInput;
    }

    setResult(convertedText);
    setCurrentCaseType(type);
  };

  const copyResult = async () => {
    if (!result) {
      copyToClipboard('', '没有结果可复制！');
      return;
    }
    copyToClipboard(result, `已复制: ${currentCaseType || '转换结果'}`);
  };

  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-gray-700">工具</Link>
          <span>/</span>
          <span className="text-gray-900">大小写转换</span>
        </nav>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">🔤 大小写转换</h1>
          <div className="flex space-x-2">
            <Link
              href="/tools"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              🏠 返回工具列表
            </Link>
          </div>
        </div>
      </div>

      {/* 大小写转换工具 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-2">文本大小写转换</h2>
          <p className="text-gray-600 text-sm mb-4">在不同的大小写格式之间转换文本</p>

          <div className="space-y-4">
            {/* 输入区域 */}
            <div>
              <label htmlFor="text-input" className="block text-sm font-medium text-gray-700 mb-2">
                输入文本：
              </label>
              <textarea
                id="text-input"
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="在这里输入您的文本..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
              />
            </div>

            {/* 转换按钮 */}
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={() => convertCase('upper')}
                className="bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                UPPERCASE
              </Button>
              <Button
                onClick={() => convertCase('lower')}
                className="bg-emerald-500 text-white hover:bg-emerald-600 active:bg-emerald-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                lowercase
              </Button>
              <Button
                onClick={() => convertCase('title')}
                className="bg-purple-500 text-white hover:bg-purple-600 active:bg-purple-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                Title Case
              </Button>
              <Button
                onClick={() => convertCase('camel')}
                className="bg-orange-500 text-white hover:bg-orange-600 active:bg-orange-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                camelCase
              </Button>
              <Button
                onClick={() => convertCase('pascal')}
                className="bg-pink-500 text-white hover:bg-pink-600 active:bg-pink-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                PascalCase
              </Button>
              <Button
                onClick={() => convertCase('snake')}
                className="bg-indigo-500 text-white hover:bg-indigo-600 active:bg-indigo-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                snake_case
              </Button>
              <Button
                onClick={() => convertCase('kebab')}
                className="bg-teal-500 text-white hover:bg-teal-600 active:bg-teal-700 border-0 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-4 py-2.5 rounded-lg"
              >
                kebab-case
              </Button>
            </div>

            {/* 结果区域 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="result-output" className="block text-sm font-medium text-gray-700">
                  转换结果：
                </label>
                {result && currentCaseType && (
                  <ShareButton
                    toolId="case-converter"
                    toolName="大小写转换"
                    input={textInput}
                    output={result}
                    options={{
                      caseType: currentCaseType,
                      wordCount: textInput.split(/\s+/).length,
                      charCount: textInput.length,
                      outputLength: result.length,
                    }}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
              <div className="relative">
                <textarea
                  id="result-output"
                  value={result}
                  readOnly
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 resize-vertical"
                />
                <Button
                  onClick={copyResult}
                  disabled={!result}
                  className="absolute top-2 right-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-xs disabled:opacity-50"
                >
                  📋 复制结果
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3">转换格式说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">基础格式</h4>
              <ul className="space-y-1">
                <li><strong>UPPERCASE:</strong> 全部大写</li>
                <li><strong>lowercase:</strong> 全部小写</li>
                <li><strong>Title Case:</strong> 每个单词首字母大写</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">编程格式</h4>
              <ul className="space-y-1">
                <li><strong>camelCase:</strong> 驼峰命名法</li>
                <li><strong>PascalCase:</strong> 帕斯卡命名法</li>
                <li><strong>snake_case:</strong> 下划线命名法</li>
                <li><strong>kebab-case:</strong> 短横线命名法</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 返回按钮 */}
      <div className="mt-8 text-center">
        <Link
          href="/tools"
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          ← 返回工具列表
        </Link>
      </div>

      {/* 复制成功提示 */}
      <CopyToast
        show={copyState.show}
        message={copyState.message}
        onHide={hideCopyToast}
      />
    </div>
  );
}
