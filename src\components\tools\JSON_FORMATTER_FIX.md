# JSON 格式化工具 - 无限循环修复

## 🐛 问题描述

JSON 格式化工具出现了严重的性能问题：
- 控制台不断打印 "tick" 信息
- 出现 "Maximum update depth exceeded" 错误
- 页面卡顿，CPU 使用率过高
- 组件陷入无限重新渲染循环

## 🔍 问题根源

### 原始代码问题
```typescript
// 问题代码 - 第51行
useEffect(() => {
  if (state.input.trim()) {
    const result = Formatter.format(state);
    if (result.success) {
      setState(prev => ({ ...prev, output: result.data || '' })); // 修改 state
      // ...
    }
    // ...
  }
}, [state, onUsage]); // 依赖整个 state 对象
```

### 无限循环的形成
1. **useEffect 依赖 `state`**：监听整个 state 对象的变化
2. **useEffect 内部修改 `state.output`**：调用 `setState` 更新输出
3. **state 变化触发新的 useEffect**：因为 state 是依赖项
4. **无限循环**：步骤1-3不断重复

### 具体流程
```
初始渲染 → useEffect 执行 → setState 修改 state.output → 
state 变化 → useEffect 再次执行 → setState 再次修改 → 
state 再次变化 → useEffect 再次执行 → ... (无限循环)
```

## 🔧 解决方案

### 修复策略
将 useEffect 的依赖项从整个 `state` 对象改为具体的属性，排除会被 useEffect 内部修改的 `state.output`。

### 修复后的代码
```typescript
// 修复后的代码
useEffect(() => {
  if (state.input.trim()) {
    const result = Formatter.format(state);
    if (result.success) {
      setState(prev => ({ ...prev, output: result.data || '' }));
      // ...
    }
    // ...
  }
}, [state.input, state.indent, state.sortKeys, state.validateOnly, onUsage]);
//   ↑ 只依赖输入相关的属性，不包括 output
```

### 关键改进
1. **精确依赖**：只监听真正需要触发重新格式化的属性
2. **避免循环**：不依赖会被 useEffect 内部修改的 `state.output`
3. **保持功能**：当输入内容或格式化选项变化时仍会触发重新格式化

## 📋 依赖项分析

### 需要监听的属性
- `state.input`：输入内容变化时需要重新格式化
- `state.indent`：缩进设置变化时需要重新格式化
- `state.sortKeys`：排序选项变化时需要重新格式化
- `state.validateOnly`：验证模式变化时需要重新格式化
- `onUsage`：回调函数（通常稳定，但为了完整性包含）

### 不需要监听的属性
- `state.output`：这是 useEffect 的输出结果，不应该作为输入依赖

## ✨ 修复效果

### 修复前
- ❌ 无限循环，页面卡顿
- ❌ 控制台错误信息
- ❌ CPU 使用率过高
- ❌ 用户体验极差

### 修复后
- ✅ 正常的单次执行
- ✅ 无控制台错误
- ✅ 性能正常
- ✅ 流畅的用户体验

## 🎯 功能验证

修复后的功能应该正常工作：

1. **输入变化**：输入 JSON 内容时自动格式化
2. **选项变化**：修改缩进、排序等选项时重新格式化
3. **实时验证**：显示 JSON 格式是否正确
4. **错误提示**：格式错误时显示具体错误信息
5. **性能稳定**：无无限循环，响应流畅

## 🔍 预防措施

### 最佳实践
1. **精确依赖**：useEffect 依赖项应该尽可能精确
2. **避免循环**：不要依赖会被 useEffect 内部修改的状态
3. **状态分离**：考虑将输入状态和输出状态分离
4. **性能监控**：定期检查是否有无限循环问题

### 代码审查要点
```typescript
// ❌ 危险模式 - 可能导致无限循环
useEffect(() => {
  setState(/* 修改 state */);
}, [state]); // 依赖被内部修改的状态

// ✅ 安全模式 - 精确依赖
useEffect(() => {
  setState(/* 修改 state */);
}, [state.specificProperty]); // 只依赖不会被内部修改的属性
```

## 📱 测试方法

1. **基本功能测试**：
   - 输入有效 JSON，观察格式化结果
   - 输入无效 JSON，观察错误提示
   - 修改格式化选项，观察结果变化

2. **性能测试**：
   - 打开浏览器开发者工具
   - 观察控制台是否有错误信息
   - 检查 CPU 使用率是否正常
   - 确认页面响应流畅

3. **边界测试**：
   - 输入大量 JSON 数据
   - 快速切换格式化选项
   - 清空输入后重新输入

---

**修复完成时间**: 2025年5月30日  
**问题类型**: React useEffect 无限循环  
**解决方案**: 精确化依赖项，避免循环依赖  
**影响范围**: JSON 格式化工具性能和稳定性
