import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db/mongodb';
import { Feedback } from '@/lib/db/models';
import { z } from 'zod';
import { Types } from 'mongoose';

// 更新反馈验证schema
const updateFeedbackSchema = z.object({
  status: z.enum(['pending', 'in-progress', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  adminNotes: z.string().max(1000, '管理员备注最多1000个字符').optional(),
  adminReply: z.string().max(2000, '管理员回复最多2000个字符').optional(),
});

// 获取单个反馈详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);

    // 检查权限：管理员或反馈创建者
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: '需要登录',
      }, { status: 401 });
    }

    const { id } = await params;

    // 验证ID格式
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的反馈ID',
      }, { status: 400 });
    }

    // 查询反馈
    const feedback = await Feedback.findById(id)
      .populate('userId', 'username email')
      .populate('resolvedBy', 'username email')
      .lean();

    if (!feedback) {
      return NextResponse.json({
        success: false,
        message: '反馈不存在',
      }, { status: 404 });
    }

    // 权限检查：管理员或反馈创建者
    const isAdmin = session.user.role === 'admin';
    const isOwner = feedback.userId?.toString() === session.user.id || 
                   feedback.email === session.user.email;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({
        success: false,
        message: '没有权限查看此反馈',
      }, { status: 403 });
    }

    return NextResponse.json({
      success: true,
      data: feedback,
    });

  } catch (error) {
    console.error('获取反馈详情失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取反馈详情失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

// 更新反馈（管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);

    // 检查管理员权限
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({
        success: false,
        message: '需要管理员权限',
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();

    // 验证ID格式
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的反馈ID',
      }, { status: 400 });
    }

    // 验证输入数据
    const validationResult = updateFeedbackSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        message: '输入数据验证失败',
        errors: validationResult.error.errors,
      }, { status: 400 });
    }

    const updateData = validationResult.data;

    // 如果状态更新为已解决，记录解决时间和解决人
    if (updateData.status === 'resolved') {
      updateData.resolvedAt = new Date();
      updateData.resolvedBy = new Types.ObjectId(session.user.id);
    }

    // 如果有管理员回复，设置回复时间
    if (updateData.adminReply && updateData.adminReply.trim() !== '') {
      updateData.adminReplyAt = new Date();
    }

    // 更新反馈
    const feedback = await Feedback.findByIdAndUpdate(
      id,
      {
        ...updateData,
        updatedAt: new Date(),
      },
      { new: true }
    ).populate('userId', 'username email')
     .populate('resolvedBy', 'username email');

    if (!feedback) {
      return NextResponse.json({
        success: false,
        message: '反馈不存在',
      }, { status: 404 });
    }

    // TODO: 如果有管理员回复，发送邮件通知用户

    return NextResponse.json({
      success: true,
      message: '反馈更新成功',
      data: feedback,
    });

  } catch (error) {
    console.error('更新反馈失败:', error);
    return NextResponse.json({
      success: false,
      message: '更新反馈失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

// 删除反馈（管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);

    // 检查管理员权限
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({
        success: false,
        message: '需要管理员权限',
      }, { status: 403 });
    }

    const { id } = await params;

    // 验证ID格式
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的反馈ID',
      }, { status: 400 });
    }

    // 删除反馈
    const feedback = await Feedback.findByIdAndDelete(id);

    if (!feedback) {
      return NextResponse.json({
        success: false,
        message: '反馈不存在',
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: '反馈删除成功',
    });

  } catch (error) {
    console.error('删除反馈失败:', error);
    return NextResponse.json({
      success: false,
      message: '删除反馈失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
