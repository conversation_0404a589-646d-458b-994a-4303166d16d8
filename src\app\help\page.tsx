import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui';

export default function HelpPage() {
  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <span className="text-gray-900">帮助中心</span>
        </nav>

        <h1 className="text-3xl font-bold text-gray-900">帮助中心</h1>
      </div>

      {/* 帮助内容 */}
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">快速开始</h2>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</span>
                <div>
                  <h3 className="font-medium text-gray-900">浏览工具</h3>
                  <p className="text-gray-600">访问工具页面，查看所有可用的工具分类</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</span>
                <div>
                  <h3 className="font-medium text-gray-900">选择工具</h3>
                  <p className="text-gray-600">点击您需要的工具，进入工具页面</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</span>
                <div>
                  <h3 className="font-medium text-gray-900">开始使用</h3>
                  <p className="text-gray-600">输入数据，点击转换或处理按钮即可获得结果</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">工具分类</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="mr-2">🕒</span>
                  时间工具
                </h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Unix时间戳转换</li>
                  <li>• 时区转换</li>
                  <li>• 日期格式化</li>
                </ul>
              </div>
              
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="mr-2">📝</span>
                  文本工具
                </h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 大小写转换</li>
                  <li>• 编码解码</li>
                  <li>• 文本格式化</li>
                </ul>
              </div>
              
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="mr-2">🔧</span>
                  格式化工具
                </h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• JSON格式化</li>
                  <li>• XML格式化</li>
                  <li>• CSS美化</li>
                </ul>
              </div>
              
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="mr-2">🔐</span>
                  加密工具
                </h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• MD5哈希</li>
                  <li>• Base64编码</li>
                  <li>• SHA加密</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">常见问题</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 为什么我的数据没有被保存？</h3>
                <p className="text-gray-600">A: 为了保护您的隐私，所有工具都在客户端处理数据，不会保存到服务器。</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 工具支持哪些浏览器？</h3>
                <p className="text-gray-600">A: 支持所有现代浏览器，包括 Chrome、Firefox、Safari、Edge 等。</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 如何复制结果？</h3>
                <p className="text-gray-600">A: 点击结果区域的复制按钮，或者直接选中文本进行复制。</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Q: 遇到问题如何反馈？</h3>
                <p className="text-gray-600">A: 您可以通过联系我们页面提交问题，或在 GitHub 上创建 Issue。</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">使用技巧</h2>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="text-lg">💡</span>
                <div>
                  <h3 className="font-medium text-gray-900">键盘快捷键</h3>
                  <p className="text-gray-600">大部分工具支持 Ctrl+V 粘贴和 Ctrl+C 复制</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-lg">🔄</span>
                <div>
                  <h3 className="font-medium text-gray-900">批量处理</h3>
                  <p className="text-gray-600">某些工具支持批量处理，可以一次处理多行数据</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-lg">📱</span>
                <div>
                  <h3 className="font-medium text-gray-900">移动端优化</h3>
                  <p className="text-gray-600">所有工具都针对移动设备进行了优化，支持触屏操作</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速链接 */}
      <div className="mt-8 text-center space-x-4">
        <Link
          href="/tools"
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          🔧 浏览工具
        </Link>
        <Link
          href="/contact"
          className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
        >
          📧 联系我们
        </Link>
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  );
}
