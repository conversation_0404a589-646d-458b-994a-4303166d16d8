'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardHeader, CardTitle, Badge, Button } from '@/components/ui';
import { ArrowLeft, MessageSquare, Clock, CheckCircle, XCircle, AlertCircle, User } from 'lucide-react';

interface Feedback {
  _id: string;
  type: 'bug' | 'feature' | 'improvement' | 'question' | 'other';
  title: string;
  content: string;
  email: string;
  status: 'pending' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  adminReply?: string;
  adminReplyAt?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  userId?: {
    username: string;
    email: string;
  };
  resolvedBy?: {
    username: string;
    email: string;
  };
}

interface Props {
  feedbackId: string;
}

const FeedbackDetailClient: React.FC<Props> = ({ feedbackId }) => {
  const { data: session } = useSession();
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user) {
      fetchFeedback();
    }
  }, [session, feedbackId]);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/feedback/${feedbackId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '获取反馈详情失败');
      }

      setFeedback(result.data);
    } catch (error) {
      console.error('获取反馈详情失败:', error);
      setError(error instanceof Error ? error.message : '获取反馈详情失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'in-progress':
        return <AlertCircle className="w-4 h-4" />;
      case 'resolved':
        return <CheckCircle className="w-4 h-4" />;
      case 'closed':
        return <XCircle className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'in-progress':
        return '处理中';
      case 'resolved':
        return '已解决';
      case 'closed':
        return '已关闭';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'bug':
        return 'Bug报告';
      case 'feature':
        return '功能建议';
      case 'improvement':
        return '改进建议';
      case 'question':
        return '问题咨询';
      case 'other':
        return '其他';
      default:
        return type;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '紧急';
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return priority;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (!session?.user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">请先登录</h1>
          <p className="text-gray-600 mb-6">
            您需要登录后才能查看反馈详情
          </p>
          <div className="space-x-4">
            <Button onClick={() => window.location.href = '/login'}>
              前往登录
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/register'}>
              注册账户
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-4xl mb-4">⏳</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">加载中...</h1>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-4xl mb-4">❌</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">加载失败</h1>
          <p className="text-red-500 mb-6">{error}</p>
          <div className="space-x-4">
            <Button onClick={fetchFeedback}>
              重试
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/profile'}>
              返回个人中心
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!feedback) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-4xl mb-4">📝</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">反馈不存在</h1>
          <p className="text-gray-600 mb-6">
            该反馈可能已被删除或您没有权限查看
          </p>
          <Button variant="outline" onClick={() => window.location.href = '/profile'}>
            返回个人中心
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/profile" className="inline-flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回个人中心
          </Link>
        </div>

        {/* 反馈详情 */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl mb-3">{feedback.title}</CardTitle>
                <div className="flex items-center space-x-3">
                  <Badge variant="outline">{getTypeText(feedback.type)}</Badge>
                  <Badge className={getStatusColor(feedback.status)}>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(feedback.status)}
                      <span>{getStatusText(feedback.status)}</span>
                    </div>
                  </Badge>
                  <Badge className={getPriorityColor(feedback.priority)}>
                    优先级: {getPriorityText(feedback.priority)}
                  </Badge>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 反馈内容 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">反馈内容</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700 whitespace-pre-wrap">{feedback.content}</p>
              </div>
            </div>

            {/* 管理员回复 */}
            {feedback.adminReply && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <User className="w-5 h-5 mr-2 text-blue-600" />
                  管理员回复
                </h3>
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                  <p className="text-blue-900 whitespace-pre-wrap">{feedback.adminReply}</p>
                  {feedback.adminReplyAt && (
                    <div className="text-sm text-blue-600 mt-3 flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      回复时间: {formatDate(feedback.adminReplyAt)}
                      {feedback.resolvedBy && (
                        <span className="ml-2">
                          - {feedback.resolvedBy.username}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 反馈信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">反馈信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">提交时间：</span>
                  <span className="text-gray-900">{formatDate(feedback.createdAt)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">最后更新：</span>
                  <span className="text-gray-900">{formatDate(feedback.updatedAt)}</span>
                </div>
                {feedback.resolvedAt && (
                  <div>
                    <span className="font-medium text-gray-600">解决时间：</span>
                    <span className="text-gray-900">{formatDate(feedback.resolvedAt)}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-gray-600">反馈ID：</span>
                  <span className="text-gray-900 font-mono">{feedback._id}</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-4 pt-4 border-t">
              <Button variant="outline" onClick={() => window.location.href = '/profile'}>
                返回个人中心
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/feedback'}>
                提交新反馈
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FeedbackDetailClient;
