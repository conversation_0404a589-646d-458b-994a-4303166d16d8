'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText
} from 'lucide-react';

interface DatabaseStatusData {
  connected: boolean;
  toolsInDb: number;
  toolsInConstants: number;
  needSync: boolean;
  source: 'database' | 'constants' | 'constants_fallback';
}

export default function DatabaseStatus() {
  const [status, setStatus] = useState<DatabaseStatusData | null>(null);
  const [loading, setLoading] = useState(false);

  const checkStatus = async () => {
    setLoading(true);
    try {
      // 检查数据库连接
      const dbResponse = await fetch('/api/db/test');
      const dbResult = await dbResponse.json();
      
      // 检查工具同步状态
      const toolsResponse = await fetch('/api/tools/sync?action=count');
      const toolsResult = await toolsResponse.json();
      
      // 测试工具API数据源
      const apiResponse = await fetch('/api/tools?limit=1');
      const apiResult = await apiResponse.json();

      setStatus({
        connected: dbResult.success,
        toolsInDb: toolsResult.success ? toolsResult.data.database : 0,
        toolsInConstants: toolsResult.success ? toolsResult.data.constants : 0,
        needSync: toolsResult.success ? toolsResult.data.needSync : true,
        source: apiResult.success ? apiResult.data.source : 'constants',
      });
    } catch (error) {
      console.error('检查数据库状态失败:', error);
      setStatus({
        connected: false,
        toolsInDb: 0,
        toolsInConstants: 0,
        needSync: true,
        source: 'constants',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  if (!status) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        检查数据库状态...
      </div>
    );
  }

  const getStatusIcon = () => {
    if (!status.connected) {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
    if (status.needSync) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!status.connected) {
      return '数据库未连接';
    }
    if (status.needSync) {
      return '需要同步';
    }
    return '数据库已连接';
  };

  const getSourceIcon = () => {
    switch (status.source) {
      case 'database':
        return <Database className="w-4 h-4 text-blue-500" />;
      case 'constants_fallback':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSourceText = () => {
    switch (status.source) {
      case 'database':
        return '数据库';
      case 'constants_fallback':
        return '常量(回退)';
      default:
        return '常量文件';
    }
  };

  return (
    <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg border">
      {/* 数据库状态 */}
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusText()}</span>
      </div>

      {/* 数据源 */}
      <div className="flex items-center gap-2">
        {getSourceIcon()}
        <span className="text-sm">数据源: {getSourceText()}</span>
      </div>

      {/* 工具数量 */}
      <div className="flex items-center gap-2">
        <span className="text-sm">工具:</span>
        <Badge variant="outline" className="text-xs">
          DB: {status.toolsInDb}
        </Badge>
        <Badge variant="outline" className="text-xs">
          常量: {status.toolsInConstants}
        </Badge>
      </div>

      {/* 刷新按钮 */}
      <Button
        onClick={checkStatus}
        disabled={loading}
        variant="ghost"
        size="sm"
        className="ml-auto"
      >
        <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
      </Button>

      {/* 快速操作链接 */}
      {status.connected && status.needSync && (
        <a
          href="/admin/tools"
          className="text-xs text-blue-600 hover:text-blue-800 underline"
        >
          去同步
        </a>
      )}
    </div>
  );
}
