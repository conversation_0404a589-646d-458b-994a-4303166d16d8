'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from './IOPanel';
import { JsonFormatter as Formatter } from '@/lib/utils/tools';
import { JsonFormatterState, ToolComponentProps } from '@/types/tools';
import ShareButton from '@/components/share/ShareButton';

const JsonFormatter: React.FC<ToolComponentProps> = ({
  config,
  onUsage
}) => {
  const [state, setState] = useState<JsonFormatterState>({
    input: '',
    output: '',
    indent: 2,
    sortKeys: false,
    validateOnly: false,
  });

  const [error, setError] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(false);

  // 实时格式化 - 使用防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      if (state.input.trim()) {
        const result = Formatter.format(state);
        if (result.success) {
          setState(prev => ({ ...prev, output: result.data || '' }));
          setError('');
          setIsValid(true);

          // 记录使用
          onUsage?.('use', {
            indent: state.indent,
            sortKeys: state.sortKeys,
            validateOnly: state.validateOnly,
            inputSize: state.input.length,
            outputSize: result.data?.length || 0,
          });
        } else {
          setError(result.error || '格式化失败');
          setState(prev => ({ ...prev, output: '' }));
          setIsValid(false);
        }
      } else {
        setState(prev => ({ ...prev, output: '' }));
        setError('');
        setIsValid(false);
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [state.input, state.indent, state.sortKeys, state.validateOnly]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleInputChange = (value: string) => {
    setState(prev => ({ ...prev, input: value }));
  };

  const handleClear = () => {
    setState(prev => ({ ...prev, input: '', output: '' }));
    setError('');
    setIsValid(false);
  };

  const handleExample = () => {
    const example = `{"name":"John Doe","age":30,"city":"New York","hobbies":["reading","swimming","coding"],"address":{"street":"123 Main St","zipCode":"10001"}}`;
    setState(prev => ({ ...prev, input: example }));
  };

  const handleMinify = () => {
    if (state.input.trim()) {
      const result = Formatter.minify(state.input);
      if (result.success) {
        setState(prev => ({ ...prev, output: result.data || '' }));
        setError('');
        onUsage?.('minify', result.metadata);
      } else {
        setError(result.error || '压缩失败');
      }
    }
  };

  const handleCopyOutput = async () => {
    if (state.output) {
      try {
        await navigator.clipboard.writeText(state.output);
        onUsage?.('copy');
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const handleDownload = () => {
    if (state.output) {
      const blob = new Blob([state.output], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'formatted.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      onUsage?.('download');
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
    { label: '压缩', icon: '📦', onClick: handleMinify, disabled: !isValid },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !state.output
    },
    {
      label: '下载',
      icon: '💾',
      onClick: handleDownload,
      disabled: !state.output
    },
  ];

  return (
    <div className="space-y-6">
      {/* 工具选项 */}
      <Card>
        <CardHeader>
          <CardTitle>格式化选项</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 缩进大小 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                缩进大小
              </label>
              <select
                value={state.indent}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  indent: parseInt(e.target.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value={2}>2 空格</option>
                <option value={4}>4 空格</option>
                <option value={8}>8 空格</option>
                <option value={0}>压缩</option>
              </select>
            </div>

            {/* 排序键 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                排序选项
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={state.sortKeys}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    sortKeys: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">按键名排序</span>
              </label>
            </div>

            {/* 验证模式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                验证模式
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={state.validateOnly}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    validateOnly: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">仅验证格式</span>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 状态指示器 */}
      {state.input.trim() && (
        <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${isValid ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span className="text-sm font-medium">
                {isValid ? '✅ JSON格式正确' : '❌ JSON格式错误'}
              </span>
            </div>

            {isValid && state.output && (
              <>
                <div className="text-sm text-gray-500">
                  行数: {state.output.split('\n').length}
                </div>
                <div className="text-sm text-gray-500">
                  字符: {state.output.length}
                </div>
              </>
            )}
          </div>

          {/* 分享按钮 */}
          {isValid && state.output && (
            <ShareButton
              toolId="json-formatter"
              toolName="JSON 格式化工具"
              input={state.input}
              output={state.output}
              options={{
                indent: state.indent,
                sortKeys: state.sortKeys,
                validateOnly: state.validateOnly,
                isValid,
                lineCount: state.output.split('\n').length,
                charCount: state.output.length,
              }}
              size="sm"
              showText={false}
            />
          )}
        </div>
      )}

      {/* 输入输出面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入面板 */}
        <Card className="h-96">
          <IOPanel
            title="JSON 输入"
            placeholder='请输入JSON数据...\n例如: {"name": "John", "age": 30}'
            value={state.input}
            onChange={handleInputChange}
            actions={inputActions}
            maxLength={config?.maxInputSize || 1048576}
            language="json"
            showLineNumbers
            error={error}
          />
        </Card>

        {/* 输出面板 */}
        <Card className="h-96">
          <IOPanel
            title="格式化输出"
            placeholder="格式化结果将显示在这里..."
            value={state.output}
            readonly
            actions={outputActions}
            language="json"
            showLineNumbers
          />
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, indent: 2, sortKeys: false }))}
            >
              标准格式化
            </Button>

            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, indent: 0 }))}
            >
              压缩JSON
            </Button>

            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, sortKeys: true }))}
            >
              排序键名
            </Button>

            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, validateOnly: true }))}
            >
              验证格式
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>格式化:</strong> 将压缩的JSON数据格式化为易读的格式</p>
            <p><strong>压缩:</strong> 移除所有不必要的空白字符，减小文件大小</p>
            <p><strong>验证:</strong> 检查JSON数据的语法是否正确</p>
            <p><strong>排序:</strong> 按字母顺序排列对象的键名</p>
            <p><strong>支持功能:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>实时语法验证和错误提示</li>
              <li>行号显示，便于定位错误</li>
              <li>一键复制和下载结果</li>
              <li>支持大文件处理（最大1MB）</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default JsonFormatter;
