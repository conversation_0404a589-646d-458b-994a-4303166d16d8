import React from 'react';
import Link from 'next/link';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, ExternalLink } from 'lucide-react';
import { useToolsStore } from '@/store';
import { Tool } from '@/types/tools';

interface ToolCardProps {
  tool: Tool;
  size?: 'small' | 'medium' | 'large';
  onClick?: (toolId: string) => void;
  onFavorite?: (toolId: string) => void;
  className?: string;
}

export default function ToolCard({
  tool,
  size = 'medium',
  onClick,
  onFavorite,
  className = ''
}: ToolCardProps) {
  const { favoriteTools, toggleFavorite } = useToolsStore();
  const isFavorite = favoriteTools?.includes(tool.id) || false;

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onFavorite) {
      onFavorite(tool.id);
    } else {
      toggleFavorite(tool.id);
    }
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(tool.id);
    }
  };

  const sizeClasses = {
    small: 'p-3',
    medium: 'p-4',
    large: 'p-6',
  };

  const iconSizes = {
    small: 'w-8 h-8 text-base',
    medium: 'w-10 h-10 text-lg',
    large: 'w-12 h-12 text-xl',
  };

  return (
    <Link href={tool.path} className={`block ${className}`}>
      <Card 
        variant="tool" 
        className={`group cursor-pointer hover:shadow-lg transition-all duration-200 ${sizeClasses[size]}`}
        onClick={handleCardClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`bg-primary-100 rounded-lg flex items-center justify-center ${iconSizes[size]}`}>
                <span className="text-primary-600">{tool.icon}</span>
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="group-hover:text-primary-600 transition-colors truncate">
                  {tool.name}
                </CardTitle>
                {size !== 'small' && (
                  <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                    {tool.description}
                  </p>
                )}
              </div>
            </div>
            
            {/* 收藏按钮 */}
            <button
              onClick={handleFavoriteClick}
              className={`p-1 rounded-full transition-colors ${
                isFavorite 
                  ? 'text-red-500 hover:text-red-600' 
                  : 'text-gray-300 hover:text-red-500'
              }`}
              title={isFavorite ? '取消收藏' : '添加到收藏'}
            >
              <Heart 
                className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} 
              />
            </button>
          </div>
        </CardHeader>

        {size !== 'small' && (
          <CardContent className="pt-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {/* 分类标签 */}
                <Badge variant="outline" className="text-xs">
                  {tool.category}
                </Badge>
                
                {/* 新工具标识 */}
                {tool.isNew && (
                  <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                    新
                  </Badge>
                )}
                
                {/* 需要登录标识 */}
                {tool.requiredAuth && (
                  <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-800">
                    需登录
                  </Badge>
                )}
              </div>
              
              {/* 外部链接图标 */}
              <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-primary-600 transition-colors" />
            </div>
            
            {/* 工具标签 */}
            {tool.tags && tool.tags.length > 0 && size === 'large' && (
              <div className="flex flex-wrap gap-1 mt-3">
                {tool.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
                {tool.tags.length > 3 && (
                  <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    +{tool.tags.length - 3}
                  </span>
                )}
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </Link>
  );
}
