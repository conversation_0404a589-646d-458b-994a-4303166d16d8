'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useToolsStore } from '@/store';
import UnixTimestampConverter from './UnixTimestampConverter';
import { TOOLS } from '@/lib/constants/tools';

export default function TimestampClient() {
  const { addRecentTool } = useToolsStore();

  const tool = TOOLS.find(t => t.id === 'timestamp-converter');

  useEffect(() => {
    // 记录最近使用的工具
    addRecentTool('timestamp-converter');
  }, [addRecentTool]);

  const handleUsage = (action: string, metadata?: Record<string, unknown>) => {
    console.log('Tool usage:', { action, metadata });
    // 这里可以发送使用统计到API
  };

  if (!tool) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">工具未找到</h1>
          <p className="text-gray-600">请检查URL是否正确</p>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-gray-700">工具</Link>
          <span>/</span>
          <span className="text-gray-900">{tool.name}</span>
        </nav>

        {/* 工具信息 */}
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <span className="text-2xl">{tool.icon}</span>
              <h1 className="text-2xl font-bold text-gray-900">{tool.name}</h1>
            </div>
            <p className="text-gray-600 mb-4">{tool.description}</p>

            {/* 标签 */}
            <div className="flex flex-wrap gap-2">
              {tool.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2">
            <Link
              href="/tools/timestamp/features"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span className="mr-2">✨</span>
              功能特性
            </Link>
            <Link
              href="/tools/case-converter"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span className="mr-2">🔤</span>
              大小写转换
            </Link>
            <button
              onClick={() => handleUsage('favorite')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span className="mr-2">⭐</span>
              收藏
            </button>
          </div>
        </div>
      </div>

      {/* Unix 时间戳转换器组件 */}
      <UnixTimestampConverter
        toolId={tool.id}
        config={tool.config}
        onUsage={handleUsage}
      />
    </div>
  );
}
