'use client';

import React, { useState, useEffect } from 'react';
import { useQuickSearch } from './QuickSearchProvider';

interface QuickSearchButtonProps {
  className?: string;
  variant?: 'button' | 'input';
}

const QuickSearchButton: React.FC<QuickSearchButtonProps> = ({
  className = '',
  variant = 'input'
}) => {
  const [isClient, setIsClient] = useState(false);
  const { open } = useQuickSearch();

  // 确保只在客户端渲染时才显示交互元素
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (variant === 'button') {
    return (
      <button
        onClick={isClient ? open : undefined}
        className={`flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors ${className}`}
        title={isClient ? "快速搜索 (Ctrl+K)" : "搜索"}
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <span className="hidden sm:inline">搜索</span>
      </button>
    );
  }

  return (
    <button
      onClick={isClient ? open : undefined}
      className={`flex items-center w-full max-w-sm px-3 py-2 text-left text-gray-500 bg-gray-100 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors ${className}`}
    >
      <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
      <span className="flex-1">搜索工具...</span>
      {isClient && (
        <div className="flex items-center space-x-1 text-xs">
          <kbd className="px-1.5 py-0.5 bg-white border border-gray-300 rounded text-gray-600">
            Ctrl
          </kbd>
          <span>+</span>
          <kbd className="px-1.5 py-0.5 bg-white border border-gray-300 rounded text-gray-600">
            K
          </kbd>
        </div>
      )}
    </button>
  );
};

export default QuickSearchButton;
