/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'api.qrserver.com',
        port: '',
        pathname: '/v1/create-qr-code/**',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/tools/timestamp/case-converter',
        destination: '/tools/case-converter',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
