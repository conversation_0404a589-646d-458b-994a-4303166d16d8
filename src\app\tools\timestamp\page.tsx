import { Metadata } from 'next';
import TimestampClient from './TimestampClient';

export const metadata: Metadata = {
  title: 'Unix时间戳转换器 - 在线时间格式转换工具 | Tool List',
  description: '免费的Unix时间戳转换器，支持时间戳转日期、日期转时间戳，多时区转换，毫秒级精度。开发者必备的在线时间格式转换工具，支持秒、毫秒、微秒和纳秒。',
  keywords: [
    'Unix时间戳', '时间戳转换', 'epoch转换', '日期转换', '开发者工具',
    '时间格式转换', '毫秒时间戳', '在线工具', 'timestamp converter',
    '时区转换', '相对时间', '时间戳工具', '开发工具'
  ],
  openGraph: {
    title: 'Unix时间戳转换器 - 免费在线工具',
    description: '支持时间戳转日期、日期转时间戳，多时区转换，开发者必备工具',
    url: 'https://cypress.fun/tools/timestamp',
    siteName: 'Tool List',
    images: [
      {
        url: 'https://cypress.fun/og-timestamp.png',
        width: 1200,
        height: 630,
        alt: 'Unix时间戳转换器',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Unix时间戳转换器 - Tool List',
    description: '免费的Unix时间戳转换工具，支持多种时间格式转换',
    images: ['https://cypress.fun/og-timestamp.png'],
  },
  alternates: {
    canonical: 'https://cypress.fun/tools/timestamp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function TimestampPage() {
  return <TimestampClient />;
}
