import connectDB from './mongodb';
import Tool from '../models/Tool';
import User from '../models/User';
import { TOOLS } from '../constants/tools';

// 初始化数据库数据
export async function seedDatabase() {
  try {
    console.log('🌱 开始初始化数据库数据...');

    // 连接数据库
    await connectDB();

    // 清空现有数据（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('🧹 清空现有数据...');
      await Tool.deleteMany({});
      await User.deleteMany({});
    }

    // 创建默认管理员用户
    await seedUsers();

    // 创建工具数据
    await seedTools();

    console.log('✅ 数据库初始化完成');

    return {
      success: true,
      message: '数据库初始化完成',
    };
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
}

// 创建用户数据
async function seedUsers() {
  console.log('👥 创建用户数据...');

  const users = [
    {
      email: '<EMAIL>',
      name: '管理员',
      role: 'admin',
      isEmailVerified: true,
      preferences: {
        theme: 'system',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        notifications: {
          email: true,
          browser: true,
        },
      },
      favoriteTools: ['timestamp-converter', 'json-formatter'],
      recentTools: [
        {
          toolId: 'timestamp-converter',
          lastUsed: new Date(),
        },
        {
          toolId: 'json-formatter',
          lastUsed: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
        },
      ],
    },
    {
      email: '<EMAIL>',
      name: '测试用户',
      role: 'user',
      isEmailVerified: true,
      preferences: {
        theme: 'light',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        notifications: {
          email: true,
          browser: false,
        },
      },
      favoriteTools: ['text-converter'],
      recentTools: [
        {
          toolId: 'text-converter',
          lastUsed: new Date(),
        },
      ],
    },
  ];

  for (const userData of users) {
    const existingUser = await User.findOne({ email: userData.email });
    if (!existingUser) {
      await User.create(userData);
      console.log(`✅ 创建用户: ${userData.email}`);
    } else {
      console.log(`⏭️  用户已存在: ${userData.email}`);
    }
  }
}

// 创建工具数据
async function seedTools() {
  console.log('🔧 创建工具数据...');

  // 转换工具数据格式
  const toolsData = TOOLS.map((tool, index) => ({
    name: tool.name,
    description: tool.description,
    icon: tool.icon,
    category: tool.category,
    tags: tool.tags,
    path: tool.path,
    isPublic: tool.isPublic,
    requiredAuth: tool.requiredAuth,
    visitCount: Math.floor(Math.random() * 1000), // 随机访问次数
    addedBy: 'system',
    config: tool.config,
    metadata: {
      version: '1.0.0',
      author: 'Tool List Team',
      license: 'MIT',
    },
    status: 'active',
    featured: index < 3, // 前3个工具设为推荐
    order: index,
  }));

  for (const toolData of toolsData) {
    const existingTool = await Tool.findOne({ path: toolData.path });
    if (!existingTool) {
      await Tool.create(toolData);
      console.log(`✅ 创建工具: ${toolData.name}`);
    } else {
      // 更新现有工具
      await Tool.findOneAndUpdate(
        { path: toolData.path },
        { $set: toolData },
        { new: true }
      );
      console.log(`🔄 更新工具: ${toolData.name}`);
    }
  }
}

// 创建测试数据
export async function createTestData() {
  try {
    console.log('🧪 创建测试数据...');

    await connectDB();

    // 创建更多测试工具
    const testTools = [
      {
        name: 'URL编码解码',
        description: 'URL编码和解码工具，支持批量处理',
        icon: '🔗',
        category: 'text',
        tags: ['URL', '编码', '解码', '批量'],
        path: '/url-encoder',
        isPublic: true,
        requiredAuth: false,
        visitCount: Math.floor(Math.random() * 500),
        addedBy: 'system',
        status: 'active',
        featured: false,
        order: 10,
      },
      {
        name: 'QR码生成器',
        description: '在线生成QR码，支持多种格式和自定义样式',
        icon: '📱',
        category: 'image',
        tags: ['QR码', '二维码', '生成器'],
        path: '/qr-generator',
        isPublic: true,
        requiredAuth: false,
        visitCount: Math.floor(Math.random() * 800),
        addedBy: 'system',
        status: 'active',
        featured: true,
        order: 11,
      },
      {
        name: '密码生成器',
        description: '生成安全的随机密码，可自定义长度和字符集',
        icon: '🔐',
        category: 'crypto',
        tags: ['密码', '生成器', '安全', '随机'],
        path: '/password-generator',
        isPublic: true,
        requiredAuth: false,
        visitCount: Math.floor(Math.random() * 1200),
        addedBy: 'system',
        status: 'active',
        featured: true,
        order: 12,
      },
    ];

    for (const toolData of testTools) {
      const existingTool = await Tool.findOne({ path: toolData.path });
      if (!existingTool) {
        await Tool.create(toolData);
        console.log(`✅ 创建测试工具: ${toolData.name}`);
      }
    }

    console.log('✅ 测试数据创建完成');

    return {
      success: true,
      message: '测试数据创建完成',
    };
  } catch (error) {
    console.error('❌ 测试数据创建失败:', error);
    throw error;
  }
}

// 清空数据库
export async function clearDatabase() {
  try {
    console.log('🧹 清空数据库...');

    await connectDB();

    // 清空所有集合
    await Tool.deleteMany({});
    await User.deleteMany({});

    console.log('✅ 数据库清空完成');

    return {
      success: true,
      message: '数据库清空完成',
    };
  } catch (error) {
    console.error('❌ 数据库清空失败:', error);
    throw error;
  }
}

// 获取数据库统计信息
export async function getDatabaseStats() {
  try {
    await connectDB();

    const [toolCount, userCount] = await Promise.all([
      Tool.countDocuments(),
      User.countDocuments(),
    ]);

    const toolsByCategory = await Tool.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    const popularTools = await Tool.find()
      .sort({ visitCount: -1 })
      .limit(5)
      .select('name visitCount');

    return {
      success: true,
      data: {
        totalTools: toolCount,
        totalUsers: userCount,
        toolsByCategory,
        popularTools,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('❌ 获取数据库统计失败:', error);
    throw error;
  }
}
