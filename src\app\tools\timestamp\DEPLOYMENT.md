# Unix Timestamp Converter - 部署指南

## 📁 文件结构

```
src/app/tools/timestamp/
├── index.html              # 主页面
├── styles.css              # 样式文件
├── script.js               # JavaScript 功能
├── features.html           # 功能特性页面
├── case-converter.html     # 大小写转换工具
├── sitemap.xml            # SEO 站点地图
├── robots.txt             # 搜索引擎爬虫规则
├── README.md              # 项目说明
└── DEPLOYMENT.md          # 部署指南（本文件）
```

## 🚀 部署步骤

### 1. 静态网站部署

这是一个纯静态网站，可以部署到任何静态托管服务：

#### Vercel 部署
1. 将整个 `timestamp` 文件夹上传到 Vercel
2. 设置 `index.html` 为入口文件
3. 配置自定义域名（如 unixtime.help）

#### Netlify 部署
1. 拖拽 `timestamp` 文件夹到 Netlify
2. 自动检测为静态网站
3. 配置域名和 SSL

#### GitHub Pages 部署
1. 创建 GitHub 仓库
2. 上传所有文件到根目录
3. 启用 GitHub Pages
4. 设置自定义域名

### 2. 多语言 URL 配置

为了实现多语言 SEO，需要配置 URL 重写规则：

#### Vercel 配置 (vercel.json)
```json
{
  "rewrites": [
    { "source": "/zh-CN/(.*)", "destination": "/index.html" },
    { "source": "/hi-IN/(.*)", "destination": "/index.html" },
    { "source": "/ja-JP/(.*)", "destination": "/index.html" },
    { "source": "/de-DE/(.*)", "destination": "/index.html" },
    { "source": "/en-GB/(.*)", "destination": "/index.html" },
    { "source": "/ru-RU/(.*)", "destination": "/index.html" },
    { "source": "/ko-KR/(.*)", "destination": "/index.html" },
    { "source": "/en-CA/(.*)", "destination": "/index.html" },
    { "source": "/fr-FR/(.*)", "destination": "/index.html" }
  ]
}
```

#### Netlify 配置 (_redirects)
```
/zh-CN/* /index.html 200
/hi-IN/* /index.html 200
/ja-JP/* /index.html 200
/de-DE/* /index.html 200
/en-GB/* /index.html 200
/ru-RU/* /index.html 200
/ko-KR/* /index.html 200
/en-CA/* /index.html 200
/fr-FR/* /index.html 200
```

### 3. 域名配置

#### DNS 设置
```
A     @     ***********
CNAME www   unixtime.help
```

#### SSL 证书
- Vercel/Netlify 自动提供 Let's Encrypt SSL
- 确保 HTTPS 重定向已启用

### 4. SEO 优化

#### Google Search Console
1. 添加网站属性
2. 提交 sitemap.xml
3. 验证所有语言版本

#### 站点地图提交
- 主站点地图：`https://unixtime.help/sitemap.xml`
- 包含所有语言版本的 URL

## 🔧 配置文件

### 必需文件
- `index.html` - 主页面
- `styles.css` - 样式
- `script.js` - 功能脚本
- `sitemap.xml` - SEO 站点地图
- `robots.txt` - 爬虫规则

### 可选文件
- `features.html` - 功能介绍页
- `case-converter.html` - 额外工具
- `vercel.json` 或 `_redirects` - URL 重写

## 📊 性能优化

### 1. 文件压缩
```bash
# 压缩 CSS
npx clean-css-cli styles.css -o styles.min.css

# 压缩 JavaScript
npx terser script.js -o script.min.js

# 压缩 HTML
npx html-minifier index.html -o index.min.html
```

### 2. CDN 配置
- 启用 Vercel/Netlify CDN
- 配置缓存策略
- 启用 Gzip 压缩

### 3. 图片优化
- 使用 WebP 格式
- 添加适当的 alt 标签
- 实现懒加载

## 🌍 多语言部署

### 语言检测逻辑
1. URL 路径检测（优先级最高）
2. 浏览器语言检测
3. 默认英语

### 语言切换
- 用户选择语言后更新 URL
- 保存用户语言偏好到 localStorage
- 支持直接访问语言特定 URL

## 📈 监控和分析

### Google Analytics
```html
<!-- 添加到 index.html head 部分 -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 错误监控
- 使用 Sentry 或类似服务
- 监控 JavaScript 错误
- 跟踪用户行为

## 🔒 安全配置

### Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
```

### HTTPS 强制
- 配置 HSTS 头
- 重定向 HTTP 到 HTTPS
- 使用安全的 Cookie 设置

## 🧪 测试

### 功能测试
- [ ] 时间戳转换功能
- [ ] 日期转换功能
- [ ] 复制功能（所有浏览器）
- [ ] 语言切换
- [ ] 移动端适配

### 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] Lighthouse 分数 > 90
- [ ] 移动端性能优化

### SEO 测试
- [ ] 所有语言版本可访问
- [ ] Meta 标签正确
- [ ] 站点地图有效
- [ ] 结构化数据

## 📝 维护

### 定期更新
- 检查依赖安全性
- 更新浏览器兼容性
- 优化性能指标

### 备份策略
- 定期备份源代码
- 监控网站可用性
- 准备灾难恢复计划

## 🎯 成功指标

### 技术指标
- 页面加载时间 < 2秒
- 99.9% 可用性
- 移动端友好性评分 > 95

### 用户指标
- 跳出率 < 40%
- 平均会话时长 > 2分钟
- 转换率（复制操作）> 80%

---

**部署完成后，访问 https://unixtime.help 验证所有功能正常工作！**
