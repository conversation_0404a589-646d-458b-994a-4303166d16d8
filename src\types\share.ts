// 分享功能相关类型定义

export interface ShareableState {
  toolId: string;
  toolName: string;
  input: string;
  output?: string;
  options: Record<string, unknown>;
  timestamp: number;
  version: string; // 用于兼容性检查
}

export interface ShareLink {
  id: string;
  shortId: string; // 短链接ID
  toolId: string;
  toolName: string;
  state: ShareableState;
  createdAt: Date;
  expiresAt?: Date;
  createdBy?: string; // 用户ID
  isPublic: boolean;
  password?: string; // 可选密码保护
  viewCount: number;
  lastViewedAt?: Date;
}

export interface ShareStats {
  totalShares: number;
  totalViews: number;
  popularTools: Array<{
    toolId: string;
    toolName: string;
    shareCount: number;
  }>;
  recentShares: ShareLink[];
}

export interface ShareOptions {
  includeOutput?: boolean;
  setPassword?: boolean;
  password?: string;
  expirationDays?: number;
  isPublic?: boolean;
  customMessage?: string;
}

export interface SocialSharePlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
  urlTemplate: string; // 包含占位符的URL模板
  enabled: boolean;
}

export interface ShareResult {
  success: boolean;
  shareId?: string;
  shortUrl?: string;
  fullUrl?: string;
  qrCode?: string; // Base64编码的二维码
  error?: string;
}

// 社交平台配置
export const SOCIAL_PLATFORMS: SocialSharePlatform[] = [
  {
    id: 'wechat',
    name: '微信',
    icon: '💬',
    color: '#07C160',
    urlTemplate: '', // 微信需要特殊处理
    enabled: true,
  },
  {
    id: 'qq',
    name: 'QQ',
    icon: '🐧',
    color: '#12B7F5',
    urlTemplate: 'https://connect.qq.com/widget/shareqq/index.html?url={url}&title={title}&desc={description}',
    enabled: true,
  },
  {
    id: 'weibo',
    name: '微博',
    icon: '📱',
    color: '#E6162D',
    urlTemplate: 'https://service.weibo.com/share/share.php?url={url}&title={title}&pic={image}',
    enabled: true,
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '🐦',
    color: '#1DA1F2',
    urlTemplate: 'https://twitter.com/intent/tweet?url={url}&text={title}',
    enabled: true,
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '📘',
    color: '#1877F2',
    urlTemplate: 'https://www.facebook.com/sharer/sharer.php?u={url}',
    enabled: true,
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: '💼',
    color: '#0A66C2',
    urlTemplate: 'https://www.linkedin.com/sharing/share-offsite/?url={url}',
    enabled: true,
  },
];

// 分享模板
export interface ShareTemplate {
  id: string;
  name: string;
  title: string;
  description: string;
  includeResult: boolean;
  includeTimestamp: boolean;
}

export const SHARE_TEMPLATES: ShareTemplate[] = [
  {
    id: 'simple',
    name: '简单分享',
    title: '我在使用 {toolName} 工具',
    description: '快来试试这个实用的在线工具！',
    includeResult: false,
    includeTimestamp: false,
  },
  {
    id: 'with-result',
    name: '包含结果',
    title: '我用 {toolName} 处理了数据',
    description: '查看我的处理结果：{result}',
    includeResult: true,
    includeTimestamp: false,
  },
  {
    id: 'detailed',
    name: '详细分享',
    title: '{toolName} - {timestamp}',
    description: '输入：{input}\n输出：{output}\n\n来试试这个工具吧！',
    includeResult: true,
    includeTimestamp: true,
  },
];

// 分享配置
export interface ShareConfig {
  maxSharesPerUser: number;
  defaultExpirationDays: number;
  maxExpirationDays: number;
  enablePasswordProtection: boolean;
  enableQRCode: boolean;
  enableSocialShare: boolean;
  shortUrlDomain: string;
  shortUrlLength: number;
}

export const DEFAULT_SHARE_CONFIG: ShareConfig = {
  maxSharesPerUser: 100,
  defaultExpirationDays: 30,
  maxExpirationDays: 365,
  enablePasswordProtection: true,
  enableQRCode: true,
  enableSocialShare: true,
  shortUrlDomain: 'toollist.app',
  shortUrlLength: 8,
};

// 分享事件类型
export type ShareEventType = 
  | 'share_created'
  | 'share_viewed'
  | 'share_copied'
  | 'share_social'
  | 'share_expired'
  | 'share_deleted';

export interface ShareEvent {
  id: string;
  type: ShareEventType;
  shareId: string;
  userId?: string;
  platform?: string; // 社交平台ID
  metadata?: Record<string, unknown>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}

// 分享权限
export interface SharePermission {
  canCreate: boolean;
  canView: boolean;
  canDelete: boolean;
  canSetPassword: boolean;
  canSetExpiration: boolean;
  maxShares: number;
  maxExpirationDays: number;
}

// 默认分享权限
export const DEFAULT_SHARE_PERMISSION: SharePermission = {
  canCreate: true,
  canView: true,
  canDelete: true,
  canSetPassword: true,
  canSetExpiration: true,
  maxShares: 50,
  maxExpirationDays: 90,
};

// 分享错误类型
export enum ShareErrorType {
  INVALID_TOOL = 'INVALID_TOOL',
  INVALID_STATE = 'INVALID_STATE',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  EXPIRED = 'EXPIRED',
  PASSWORD_REQUIRED = 'PASSWORD_REQUIRED',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  NOT_FOUND = 'NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RATE_LIMITED = 'RATE_LIMITED',
}

export class ShareError extends Error {
  constructor(
    public type: ShareErrorType,
    message: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ShareError';
  }
}

// 分享URL解析结果
export interface ParsedShareUrl {
  shareId: string;
  isValid: boolean;
  isExpired: boolean;
  requiresPassword: boolean;
  toolId?: string;
  error?: ShareErrorType;
}

// 分享验证结果
export interface ShareValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  canShare: boolean;
  estimatedSize: number; // 预估的分享数据大小（字节）
}
