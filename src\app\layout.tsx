import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import './globals.css';
import { Header, Footer } from '@/components/layout';
import { ErrorBoundary } from '@/components/common';
import { Providers } from '@/components/providers';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  metadataBase: new URL('https://cypress.fun'),
  title: {
    default: 'Tool List - 开发者工具集合',
    template: '%s | Tool List'
  },
  description: '专业的在线开发工具集合平台，提供时间戳转换、JSON格式化、Base64编码、颜色转换、哈希计算等11个实用工具。支持工具分享、智能搜索，为开发者提供高效便捷的在线工具服务。',
  keywords: [
    '开发工具', '在线工具', '工具集合', 'Tool List',
    '时间戳转换', 'Unix时间戳', 'JSON格式化', 'JSON验证',
    'Base64编码', '颜色转换', 'HEX转RGB', '大小写转换',
    'URL编码', 'IP地址转换', 'QR码生成', 'SHA哈希',
    '图片压缩', '文本转换', '开发者工具', '前端工具'
  ],
  authors: [{ name: 'Tool List Team', url: 'https://cypress.fun' }],
  creator: 'Tool List Team',
  publisher: 'Tool List',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://cypress.fun',
    siteName: 'Tool List',
    title: 'Tool List - 开发者工具集合',
    description: '专业的在线开发工具集合平台，提供11个实用工具，支持工具分享和智能搜索。',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Tool List - 开发者工具集合',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tool List - 开发者工具集合',
    description: '专业的在线开发工具集合平台，提供11个实用工具，支持工具分享和智能搜索。',
    images: ['/og-image.png'],
    creator: '@toollist',
  },
  alternates: {
    canonical: 'https://cypress.fun',
  },
  category: 'technology',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className={inter.variable}>
      <body className="min-h-screen bg-gray-50 font-sans antialiased">
        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-3036M4Y8W5"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-3036M4Y8W5');
          `}
        </Script>

        <Providers>
          <ErrorBoundary>
            <div className="flex flex-col min-h-screen">
              <Header />
              <main className="flex-1 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto">
                  {children}
                </div>
              </main>
              <Footer />
            </div>
          </ErrorBoundary>
        </Providers>
      </body>
    </html>
  );
}
