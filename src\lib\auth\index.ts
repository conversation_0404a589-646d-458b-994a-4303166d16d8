/* eslint-disable @typescript-eslint/no-explicit-any */
import CredentialsProvider from 'next-auth/providers/credentials';
import GitHubProvider from 'next-auth/providers/github';
import GoogleProvider from 'next-auth/providers/google';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET || 'fallback-secret-for-development',
  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'credentials',
      credentials: {
        email: { label: '邮箱', type: 'email' },
        password: { label: '密码', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // 检查是否是演示账户（优先处理，避免数据库连接问题）
          if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
            return {
              id: 'demo-admin',
              email: '<EMAIL>',
              name: '演示管理员',
              role: 'admin',
            };
          }

          if (credentials.email === '<EMAIL>' && credentials.password === 'user123') {
            return {
              id: 'demo-user',
              email: '<EMAIL>',
              name: '演示用户',
              role: 'user',
            };
          }

          // 如果没有 MongoDB URI，只允许演示账户
          if (!process.env.MONGODB_URI) {
            console.warn('MONGODB_URI 未配置，只能使用演示账户');
            return null;
          }

          // 连接数据库
          await connectDB();

          // 查找用户（包含密码字段）
          const user = await User.findByEmailOrUsername(credentials.email);

          if (!user) {
            return null;
          }

          // 验证密码
          const isPasswordValid = await user.comparePassword(credentials.password);
          if (!isPasswordValid) {
            return null;
          }

          // 更新最后登录时间
          user.lastLoginAt = new Date();
          await user.save();

          // 返回用户信息
          return {
            id: (user._id as any).toString(),
            email: user.email,
            name: user.username,
            role: user.role,
            avatar: user.avatar,
            isEmailVerified: user.isEmailVerified,
          };

        } catch (error) {
          console.error('认证错误:', error);
          return null;
        }
      },
    }),
    // 只有在环境变量配置时才添加第三方登录
    ...(process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET ? [
      GitHubProvider({
        clientId: process.env.GITHUB_CLIENT_ID,
        clientSecret: process.env.GITHUB_CLIENT_SECRET,
      })
    ] : []),
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      })
    ] : []),
  ],
  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user }: { token: any; user?: any }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
        token.avatar = user.avatar;
        token.isEmailVerified = user.isEmailVerified;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.avatar = token.avatar as string;
        session.user.isEmailVerified = token.isEmailVerified as boolean;
      }
      return session;
    },
  },
  debug: process.env.NODE_ENV === 'development',
};
