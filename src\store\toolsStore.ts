import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Tool, ToolCategory } from '@/types';

interface ToolsState {
  // 工具数据
  tools: Tool[];
  categories: ToolCategory[];
  favoriteTools: string[];
  recentTools: Array<{
    toolId: string;
    lastUsed: Date;
  }>;
  
  // UI 状态
  isLoading: boolean;
  searchQuery: string;
  selectedCategory: string | null;
  selectedTags: string[];
  
  // Actions
  setTools: (tools: Tool[]) => void;
  setCategories: (categories: ToolCategory[]) => void;
  setLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string | null) => void;
  setSelectedTags: (tags: string[]) => void;
  
  // 工具操作
  addTool: (tool: Tool) => void;
  updateTool: (toolId: string, updates: Partial<Tool>) => void;
  removeTool: (toolId: string) => void;
  
  // 收藏操作
  toggleFavorite: (toolId: string) => void;
  setFavorites: (favorites: string[]) => void;
  
  // 最近使用
  addRecentTool: (toolId: string) => void;
  setRecentTools: (recentTools: Array<{ toolId: string; lastUsed: Date }>) => void;
  
  // 获取器
  getFavoriteTools: () => Tool[];
  getRecentTools: () => Tool[];
  getToolsByCategory: (category: string) => Tool[];
  searchTools: (query: string) => Tool[];
}

export const useToolsStore = create<ToolsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      tools: [],
      categories: [],
      favoriteTools: [],
      recentTools: [],
      isLoading: false,
      searchQuery: '',
      selectedCategory: null,
      selectedTags: [],

      // 基础设置器
      setTools: (tools) => set({ tools }),
      setCategories: (categories) => set({ categories }),
      setLoading: (loading) => set({ isLoading: loading }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      setSelectedCategory: (category) => set({ selectedCategory: category }),
      setSelectedTags: (tags) => set({ selectedTags: tags }),

      // 工具操作
      addTool: (tool) =>
        set((state) => ({
          tools: [...state.tools, tool],
        })),

      updateTool: (toolId, updates) =>
        set((state) => ({
          tools: state.tools.map((tool) =>
            tool.id === toolId ? { ...tool, ...updates } : tool
          ),
        })),

      removeTool: (toolId) =>
        set((state) => ({
          tools: state.tools.filter((tool) => tool.id !== toolId),
          favoriteTools: state.favoriteTools.filter((id) => id !== toolId),
          recentTools: state.recentTools.filter((item) => item.toolId !== toolId),
        })),

      // 收藏操作
      toggleFavorite: (toolId) =>
        set((state) => {
          const isFavorite = state.favoriteTools.includes(toolId);
          return {
            favoriteTools: isFavorite
              ? state.favoriteTools.filter((id) => id !== toolId)
              : [...state.favoriteTools, toolId],
          };
        }),

      setFavorites: (favorites) => set({ favoriteTools: favorites }),

      // 最近使用
      addRecentTool: (toolId) =>
        set((state) => {
          const filtered = state.recentTools.filter((item) => item.toolId !== toolId);
          return {
            recentTools: [
              { toolId, lastUsed: new Date() },
              ...filtered.slice(0, 9), // 只保留最近10个
            ],
          };
        }),

      setRecentTools: (recentTools) => set({ recentTools }),

      // 获取器
      getFavoriteTools: () => {
        const { tools, favoriteTools } = get();
        return tools.filter((tool) => favoriteTools.includes(tool.id));
      },

      getRecentTools: () => {
        const { tools, recentTools } = get();
        return recentTools
          .map((item) => tools.find((tool) => tool.id === item.toolId))
          .filter(Boolean) as Tool[];
      },

      getToolsByCategory: (category) => {
        const { tools } = get();
        return tools.filter((tool) => tool.category === category);
      },

      searchTools: (query) => {
        const { tools } = get();
        if (!query.trim()) return tools;

        const lowercaseQuery = query.toLowerCase();
        return tools.filter(
          (tool) =>
            tool.name.toLowerCase().includes(lowercaseQuery) ||
            tool.description.toLowerCase().includes(lowercaseQuery) ||
            tool.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
        );
      },
    }),
    {
      name: 'tools-storage',
      partialize: (state) => ({
        favoriteTools: state.favoriteTools,
        recentTools: state.recentTools,
        selectedCategory: state.selectedCategory,
        selectedTags: state.selectedTags,
      }),
    }
  )
);
