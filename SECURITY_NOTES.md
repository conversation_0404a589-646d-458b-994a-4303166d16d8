# 安全配置说明

## 🔒 演示账户安全

### 重要提醒
- 演示账户信息已从公开文档中移除
- 生产环境中不应暴露演示账户凭据
- 演示功能仅在开发环境中可见

### 环境配置

#### 开发环境
```bash
# 在 .env.local 中配置演示账户（可选）
NEXT_PUBLIC_DEMO_ADMIN_EMAIL=<EMAIL>
NEXT_PUBLIC_DEMO_ADMIN_PASSWORD=your-secure-password
NEXT_PUBLIC_DEMO_USER_EMAIL=<EMAIL>
NEXT_PUBLIC_DEMO_USER_PASSWORD=your-secure-password
```

#### 生产环境
- 不要设置 `NEXT_PUBLIC_DEMO_*` 环境变量
- 演示按钮将自动隐藏
- 只允许真实用户注册和登录

### 代码中的安全措施

1. **条件显示演示功能**
   - 演示按钮仅在 `NODE_ENV === 'development'` 时显示
   - 生产环境自动隐藏敏感信息

2. **环境变量控制**
   - 演示账户信息通过环境变量配置
   - 提供默认值但建议自定义

3. **文档安全**
   - 移除了公开文档中的演示账户信息
   - 添加了"联系开发者获取"的提示

### 建议的生产环境配置

```bash
# 必需的环境变量
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-very-secure-secret-key
MONGODB_URI=your-mongodb-connection-string
MONGODB_DB=your-database-name

# 不要设置演示账户变量
# NEXT_PUBLIC_DEMO_* 变量应该留空或不设置
```

### 获取演示账户信息

如需演示账户信息用于测试：
1. 联系项目开发者
2. 查看项目内部文档
3. 在开发环境中查看源代码

### 安全检查清单

- [ ] 生产环境未设置 `NEXT_PUBLIC_DEMO_*` 变量
- [ ] `NEXTAUTH_SECRET` 使用强随机密钥
- [ ] MongoDB 连接字符串安全配置
- [ ] 演示功能仅在开发环境可见
- [ ] 公开文档不包含敏感信息

## 🛡️ 其他安全建议

1. **定期更换密钥**
   - 定期更新 `NEXTAUTH_SECRET`
   - 更新数据库访问凭据

2. **监控访问**
   - 监控异常登录尝试
   - 记录演示账户使用情况

3. **权限控制**
   - 演示账户权限最小化
   - 定期审查用户权限

这些措施确保了演示功能的安全性，同时保护了生产环境的安全。
