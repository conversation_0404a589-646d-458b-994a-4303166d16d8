'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { ShareableState } from '@/types/share';
import { useShareStore } from '@/store/shareStore';
import ShareModal from './ShareModal';

interface ShareButtonProps {
  toolId: string;
  toolName: string;
  input: string;
  output?: string;
  options?: Record<string, unknown>;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  toolId,
  toolName,
  input,
  output,
  options = {},
  className = '',
  variant = 'outline',
  size = 'sm',
  showText = true,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { isSharing } = useShareStore();

  const handleShare = () => {
    // 检查是否有内容可分享
    if (!input && !output) {
      alert('请先输入内容或生成结果后再分享');
      return;
    }

    setIsModalOpen(true);
  };

  const shareState: ShareableState = {
    toolId,
    toolName,
    input,
    output,
    options,
    timestamp: Date.now(),
    version: '1.0.0',
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleShare}
        disabled={isSharing}
        className={`flex items-center space-x-2 ${className}`}
      >
        <span className="text-lg">🔗</span>
        {showText && <span>{isSharing ? '分享中...' : '分享'}</span>}
      </Button>

      <ShareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        shareState={shareState}
      />
    </>
  );
};

export default ShareButton;
