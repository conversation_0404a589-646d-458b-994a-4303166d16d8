import { NextRequest, NextResponse } from 'next/server';
import { seedDatabase, createTestData, clearDatabase, getDatabaseStats } from '@/lib/db/seed';

export async function GET(request: NextRequest) {
  try {
    // 检查是否有数据库连接配置
    if (!process.env.MONGODB_URI) {
      return NextResponse.json({
        success: false,
        message: '数据库未配置',
        error: '请在环境变量中配置 MONGODB_URI',
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        const stats = await getDatabaseStats();
        return NextResponse.json(stats);

      default:
        return NextResponse.json({
          success: false,
          message: '不支持的操作',
          availableActions: ['stats'],
        }, { status: 400 });
    }
  } catch (error) {
    console.error('数据库管理操作失败:', error);

    return NextResponse.json({
      success: false,
      message: '数据库管理操作失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查是否有数据库连接配置
    if (!process.env.MONGODB_URI) {
      return NextResponse.json({
        success: false,
        message: '数据库未配置',
        error: '请在环境变量中配置 MONGODB_URI',
      }, { status: 503 });
    }

    const body = await request.json();
    const { action, confirm } = body;

    // 安全检查：只在开发环境允许危险操作
    if (process.env.NODE_ENV === 'production' && ['clear', 'seed'].includes(action)) {
      return NextResponse.json({
        success: false,
        message: '生产环境不允许此操作',
      }, { status: 403 });
    }

    switch (action) {
      case 'seed':
        if (!confirm) {
          return NextResponse.json({
            success: false,
            message: '请确认要初始化数据库',
            requireConfirm: true,
          }, { status: 400 });
        }

        const seedResult = await seedDatabase();
        return NextResponse.json(seedResult);

      case 'test-data':
        const testResult = await createTestData();
        return NextResponse.json(testResult);

      case 'clear':
        if (!confirm) {
          return NextResponse.json({
            success: false,
            message: '请确认要清空数据库',
            requireConfirm: true,
          }, { status: 400 });
        }

        const clearResult = await clearDatabase();
        return NextResponse.json(clearResult);

      case 'stats':
        const stats = await getDatabaseStats();
        return NextResponse.json(stats);

      default:
        return NextResponse.json({
          success: false,
          message: '不支持的操作',
          availableActions: ['seed', 'test-data', 'clear', 'stats'],
        }, { status: 400 });
    }
  } catch (error) {
    console.error('数据库管理操作失败:', error);

    return NextResponse.json({
      success: false,
      message: '数据库管理操作失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
