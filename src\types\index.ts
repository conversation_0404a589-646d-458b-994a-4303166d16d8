// 基础类型定义
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// 用户相关类型
export interface User extends BaseEntity {
  email: string;
  username: string;
  avatar?: string;
  role: 'user' | 'admin' | 'premium';
  preferences: UserPreferences;
  subscription?: UserSubscription;
  lastLoginAt: Date;
  isEmailVerified: boolean;
  twoFactorEnabled: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  favoriteTools: string[];
  recentTools: Array<{
    toolId: string;
    lastUsed: Date;
  }>;
}

export interface UserSubscription {
  plan: 'free' | 'pro' | 'enterprise';
  expiresAt: Date;
  features: string[];
}

// 工具相关类型
export interface Tool extends BaseEntity {
  name: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  requiredAuth: boolean;
  visitCount: number;
  lastVisited?: Date;
  addedBy: string;
}

export interface ToolCategory extends BaseEntity {
  name: string;
  icon: string;
  color: string;
  description: string;
  parentId?: string;
  order: number;
  isPublic: boolean;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 表单类型
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
}

// 组件 Props 类型
export interface ToolCardProps {
  tool: Tool;
  size?: 'small' | 'medium' | 'large';
  onClick: (toolId: string) => void;
  onFavorite?: (toolId: string) => void;
}

export interface IOPanelProps {
  title: string;
  placeholder?: string;
  value: string;
  onChange?: (value: string) => void;
  readonly?: boolean;
  language?: string;
  actions?: Array<{
    label: string;
    icon: string;
    onClick: () => void;
    disabled?: boolean;
  }>;
  maxLength?: number;
  showLineNumbers?: boolean;
}

// 通知类型
export interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    onClick: () => void;
  }>;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'auto';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 工具执行结果类型
export interface ToolResult {
  success: boolean;
  result?: unknown;
  error?: string;
  executionTime?: number;
}
