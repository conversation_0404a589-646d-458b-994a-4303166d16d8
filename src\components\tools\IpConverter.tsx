'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from './IOPanel';
import { IpConverter as Converter } from '@/lib/utils/tools';
import { IpConverterState, ToolComponentProps } from '@/types/tools';

const IpConverter: React.FC<ToolComponentProps> = ({
  config,
  onUsage
}) => {
  const [state, setState] = useState<IpConverterState>({
    input: '',
    output: '',
    inputType: 'ipv4',
    outputType: 'decimal',
  });

  const [error, setError] = useState<string>('');

  // 实时转换 - 使用防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      if (state.input.trim()) {
        const result = Converter.convert(state);
        if (result.success) {
          setState(prev => ({ ...prev, output: result.data || '' }));
          setError('');

          // 记录使用
          onUsage?.('use', {
            inputType: state.inputType,
            outputType: state.outputType,
            inputLength: state.input.length,
            outputLength: result.data?.length || 0,
          });
        } else {
          setError(result.error || '转换失败');
          setState(prev => ({ ...prev, output: '' }));
        }
      } else {
        setState(prev => ({ ...prev, output: '' }));
        setError('');
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [state.input, state.inputType, state.outputType]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleInputChange = (value: string) => {
    setState(prev => ({ ...prev, input: value }));
  };

  const handleClear = () => {
    setState(prev => ({ ...prev, input: '', output: '' }));
    setError('');
  };

  const handleExample = () => {
    const examples: Record<string, string> = {
      ipv4: '***********',
      decimal: '3232235777',
      hex: '0xC0A80101',
      binary: '11000000101010000000000100000001',
    };

    setState(prev => ({
      ...prev,
      input: examples[state.inputType] || '***********'
    }));
  };

  const handleCopyOutput = async () => {
    if (state.output) {
      try {
        await navigator.clipboard.writeText(state.output);
        onUsage?.('copy');
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !state.output
    },
  ];

  const inputTypes = [
    { value: 'ipv4', label: 'IPv4地址' },
    { value: 'decimal', label: '十进制' },
    { value: 'hex', label: '十六进制' },
    { value: 'binary', label: '二进制' },
  ];

  const outputTypes = [
    { value: 'ipv4', label: 'IPv4地址' },
    { value: 'decimal', label: '十进制' },
    { value: 'hex', label: '十六进制' },
    { value: 'binary', label: '二进制' },
  ];

  return (
    <div className="space-y-6">
      {/* 转换选项 */}
      <Card>
        <CardHeader>
          <CardTitle>转换设置</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 输入格式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                输入格式
              </label>
              <select
                value={state.inputType}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  inputType: e.target.value as IpConverterState['inputType']
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {inputTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 输出格式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                输出格式
              </label>
              <select
                value={state.outputType}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  outputType: e.target.value as IpConverterState['outputType']
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {outputTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 输入输出面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入面板 */}
        <Card className="h-96">
          <IOPanel
            title="IP地址输入"
            placeholder="请输入要转换的IP地址..."
            value={state.input}
            onChange={handleInputChange}
            actions={inputActions}
            maxLength={config?.maxInputSize || 1024}
            error={error}
          />
        </Card>

        {/* 输出面板 */}
        <Card className="h-96">
          <IOPanel
            title="转换结果"
            placeholder="转换结果将显示在这里..."
            value={state.output}
            readonly
            actions={outputActions}
          />
        </Card>
      </div>

      {/* 快速转换 */}
      <Card>
        <CardHeader>
          <CardTitle>快速转换</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {inputTypes.map((inputType) => (
              outputTypes.map((outputType) => (
                inputType.value !== outputType.value && (
                  <Button
                    key={`${inputType.value}-${outputType.value}`}
                    variant="outline"
                    size="sm"
                    onClick={() => setState(prev => ({
                      ...prev,
                      inputType: inputType.value as IpConverterState['inputType'],
                      outputType: outputType.value as IpConverterState['outputType']
                    }))}
                    className="text-xs"
                  >
                    {inputType.label} → {outputType.label}
                  </Button>
                )
              ))
            )).flat()}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>IPv4地址:</strong> 标准的点分十进制格式，如 ***********</p>
            <p><strong>十进制:</strong> IP地址的十进制表示，如 3232235777</p>
            <p><strong>十六进制:</strong> IP地址的十六进制表示，如 0xC0A80101</p>
            <p><strong>二进制:</strong> IP地址的二进制表示，如 11000000101010000000000100000001</p>
            <p><strong>支持功能:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>实时转换，输入即转换</li>
              <li>支持多种IP地址格式</li>
              <li>一键复制转换结果</li>
              <li>智能错误提示</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IpConverter;
