'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';

interface DatabaseStats {
  totalTools: number;
  totalUsers: number;
  toolsByCategory: Array<{ _id: string; count: number }>;
  popularTools: Array<{ name: string; visitCount: number }>;
  timestamp: string;
}

interface ConnectionStatus {
  status: string;
  connectionTime: string;
  timestamp: string;
  database: string;
  uri: string;
}

export default function DatabaseAdminPage() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // 测试数据库连接
  const testConnection = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/db/test');
      const result = await response.json();
      
      if (result.success) {
        setConnectionStatus(result.data);
        setMessage('✅ 数据库连接成功');
      } else {
        setMessage(`❌ 连接失败: ${result.error}`);
      }
    } catch (error) {
      setMessage(`❌ 连接失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取数据库统计
  const getStats = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/db/manage?action=stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
        setMessage('✅ 统计信息获取成功');
      } else {
        setMessage(`❌ 获取统计失败: ${result.error}`);
      }
    } catch (error) {
      setMessage(`❌ 获取统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据库
  const seedDatabase = async () => {
    if (!confirm('确定要初始化数据库吗？这将创建默认数据。')) {
      return;
    }
    
    setLoading(true);
    
    try {
      const response = await fetch('/api/db/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'seed',
          confirm: true,
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setMessage('✅ 数据库初始化成功');
        await getStats(); // 刷新统计
      } else {
        setMessage(`❌ 初始化失败: ${result.error}`);
      }
    } catch (error) {
      setMessage(`❌ 初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 创建测试数据
  const createTestData = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/db/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test-data',
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setMessage('✅ 测试数据创建成功');
        await getStats(); // 刷新统计
      } else {
        setMessage(`❌ 创建失败: ${result.error}`);
      }
    } catch (error) {
      setMessage(`❌ 创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时测试连接
  useEffect(() => {
    testConnection();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">数据库管理</h1>
        <p className="text-lg text-gray-600">
          管理和监控 MongoDB 数据库连接和数据
        </p>
      </div>

      {/* 消息显示 */}
      {message && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.includes('✅') 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 连接状态 */}
        <Card>
          <CardHeader>
            <CardTitle>连接状态</CardTitle>
          </CardHeader>
          <CardContent>
            {connectionStatus ? (
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">状态:</span>
                  <span className={`font-medium ${
                    connectionStatus.status === 'connected' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {connectionStatus.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">连接时间:</span>
                  <span className="font-medium">{connectionStatus.connectionTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">数据库:</span>
                  <span className="font-medium">{connectionStatus.database}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">URI:</span>
                  <span className="font-medium text-sm break-all">{connectionStatus.uri}</span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">正在检查连接状态...</p>
            )}
            
            <div className="mt-4 space-x-2">
              <Button 
                onClick={testConnection} 
                disabled={loading}
                size="sm"
              >
                {loading ? '测试中...' : '重新测试'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 数据库统计 */}
        <Card>
          <CardHeader>
            <CardTitle>数据库统计</CardTitle>
          </CardHeader>
          <CardContent>
            {stats ? (
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">工具总数:</span>
                  <span className="font-medium">{stats.totalTools}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">用户总数:</span>
                  <span className="font-medium">{stats.totalUsers}</span>
                </div>
                
                {stats.toolsByCategory.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">分类统计:</h4>
                    <div className="space-y-1">
                      {stats.toolsByCategory.map((category) => (
                        <div key={category._id} className="flex justify-between text-sm">
                          <span className="text-gray-600">{category._id}:</span>
                          <span>{category.count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {stats.popularTools.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">热门工具:</h4>
                    <div className="space-y-1">
                      {stats.popularTools.map((tool, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span className="text-gray-600">{tool.name}:</span>
                          <span>{tool.visitCount} 次</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">暂无统计数据</p>
            )}
            
            <div className="mt-4">
              <Button 
                onClick={getStats} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                {loading ? '获取中...' : '刷新统计'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 数据库操作 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>数据库操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">初始化数据库</h4>
              <p className="text-sm text-gray-600 mb-3">
                创建默认的工具和用户数据
              </p>
              <Button 
                onClick={seedDatabase} 
                disabled={loading}
                variant="primary"
              >
                {loading ? '初始化中...' : '初始化数据库'}
              </Button>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">创建测试数据</h4>
              <p className="text-sm text-gray-600 mb-3">
                添加额外的测试工具和数据
              </p>
              <Button 
                onClick={createTestData} 
                disabled={loading}
                variant="outline"
              >
                {loading ? '创建中...' : '创建测试数据'}
              </Button>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">⚠️ 注意事项</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 初始化数据库会清空现有数据（仅开发环境）</li>
              <li>• 请确保 MongoDB 服务正在运行</li>
              <li>• 生产环境不允许执行危险操作</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
