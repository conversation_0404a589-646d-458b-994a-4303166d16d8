{"name": "toollist", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@auth/mongodb-adapter": "^3.9.1", "@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.77.1", "@types/crypto-js": "^4.2.2", "@vercel/analytics": "^1.5.0", "antd": "^5.22.2", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^12.12.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "next": "15.3.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "zod": "^3.25.49", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.77.2", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}