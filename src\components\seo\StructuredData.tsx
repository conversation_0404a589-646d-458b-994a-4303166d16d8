import { StructuredData, generateStructuredDataScript } from '@/lib/structured-data';

interface StructuredDataProps {
  data: StructuredData | StructuredData[];
}

export const StructuredDataComponent: React.FC<StructuredDataProps> = ({ data }) => {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={generateStructuredDataScript(data)}
    />
  );
};

export default StructuredDataComponent;
