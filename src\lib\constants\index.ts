// 应用常量
export const APP_NAME = 'Tool List';
export const APP_DESCRIPTION = '一个集成多种常用开发工具的在线平台';
export const APP_VERSION = '1.0.0';

// 路由常量
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  TOOLS: {
    BASE: '/tools',
    TIMESTAMP: '/tools/timestamp',
    JSON_FORMATTER: '/tools/json-formatter',
    TEXT_CONVERTER: '/tools/text-converter',
    IP_CONVERTER: '/tools/ip-converter',
    NAVIGATION: '/tools/navigation',
  },
  ADMIN: {
    BASE: '/admin',
    USERS: '/admin/users',
    TOOLS: '/admin/tools',
    ANALYTICS: '/admin/analytics',
  },
  API: {
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      LOGOUT: '/api/auth/logout',
      REFRESH: '/api/auth/refresh',
      RESET_PASSWORD: '/api/auth/reset-password',
    },
    TOOLS: {
      BASE: '/api/tools',
      CATEGORIES: '/api/tools/categories',
      FAVORITES: '/api/tools/favorites',
    },
    USER: {
      PROFILE: '/api/user/profile',
      PREFERENCES: '/api/user/preferences',
    },
  },
} as const;

// 工具分类
export const TOOL_CATEGORIES = [
  {
    id: 'time',
    name: '时间工具',
    icon: 'Clock',
    color: '#3b82f6',
    description: '时间戳转换、时区转换等时间相关工具',
  },
  {
    id: 'text',
    name: '文本工具',
    icon: 'Type',
    color: '#10b981',
    description: '文本格式转换、编码解码等文本处理工具',
  },
  {
    id: 'format',
    name: '格式工具',
    icon: 'Code',
    color: '#f59e0b',
    description: 'JSON格式化、XML格式化等数据格式工具',
  },
  {
    id: 'network',
    name: '网络工具',
    icon: 'Globe',
    color: '#ef4444',
    description: 'IP查询、域名解析等网络相关工具',
  },
  {
    id: 'image',
    name: '图片工具',
    icon: 'Image',
    color: '#8b5cf6',
    description: '图片压缩、格式转换等图片处理工具',
  },
  {
    id: 'crypto',
    name: '加密工具',
    icon: 'Shield',
    color: '#06b6d4',
    description: '哈希计算、加密解密等安全工具',
  },
] as const;

// 工具列表
export const TOOLS = [
  {
    id: 'timestamp-converter',
    name: 'Unix时间戳转换',
    description: '在Unix时间戳和标准时间格式之间进行转换',
    icon: 'Clock',
    category: 'time',
    tags: ['时间戳', 'Unix', '时间转换'],
    isPublic: true,
    requiredAuth: false,
    path: ROUTES.TOOLS.TIMESTAMP,
  },
  {
    id: 'json-formatter',
    name: 'JSON格式化',
    description: 'JSON数据格式化、压缩和验证',
    icon: 'Braces',
    category: 'format',
    tags: ['JSON', '格式化', '验证'],
    isPublic: true,
    requiredAuth: false,
    path: ROUTES.TOOLS.JSON_FORMATTER,
  },
  {
    id: 'text-converter',
    name: '文本转换',
    description: '大小写转换、编码转换、命名格式转换',
    icon: 'Type',
    category: 'text',
    tags: ['文本', '大小写', '编码'],
    isPublic: true,
    requiredAuth: false,
    path: ROUTES.TOOLS.TEXT_CONVERTER,
  },
  {
    id: 'ip-converter',
    name: 'IP地址转换',
    description: 'IP地址格式转换和查询',
    icon: 'Globe',
    category: 'network',
    tags: ['IP', '网络', '地址'],
    isPublic: true,
    requiredAuth: false,
    path: ROUTES.TOOLS.IP_CONVERTER,
  },
  {
    id: 'navigation',
    name: '导航网站',
    description: '常用网站导航和书签管理',
    icon: 'Compass',
    category: 'network',
    tags: ['导航', '书签', '网站'],
    isPublic: true,
    requiredAuth: true,
    path: ROUTES.TOOLS.NAVIGATION,
  },
] as const;

// 主题配置
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto',
} as const;

// 语言配置
export const LANGUAGES = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US',
} as const;

// 用户角色
export const USER_ROLES = {
  USER: 'user',
  ADMIN: 'admin',
  PREMIUM: 'premium',
} as const;

// 订阅计划
export const SUBSCRIPTION_PLANS = {
  FREE: 'free',
  PRO: 'pro',
  ENTERPRISE: 'enterprise',
} as const;

// 文件上传限制
export const UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_FILE_TYPES: [
    'text/plain',
    'application/json',
    'text/csv',
    'application/xml',
  ],
} as const;

// API 限制
export const API_LIMITS = {
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15分钟
  RATE_LIMIT_MAX_REQUESTS: 100,
  REQUEST_TIMEOUT: 30 * 1000, // 30秒
} as const;

// 缓存配置
export const CACHE_KEYS = {
  USER_PREFERENCES: 'user_preferences',
  TOOL_FAVORITES: 'tool_favorites',
  RECENT_TOOLS: 'recent_tools',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  UNAUTHORIZED: '未授权访问，请先登录',
  FORBIDDEN: '权限不足，无法访问此资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入数据格式错误',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  RATE_LIMIT_EXCEEDED: '请求过于频繁，请稍后再试',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '退出成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  COPY_SUCCESS: '复制成功',
  UPLOAD_SUCCESS: '上传成功',
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  IPV6: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
} as const;
