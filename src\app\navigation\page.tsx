'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, CopyToast } from '@/components/ui';
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard';

interface Website {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  icon?: string;
  tags: string[];
  isBookmarked: boolean;
}

interface Category {
  id: string;
  name: string;
  icon: string;
  count: number;
}

const NavigationPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showBookmarksOnly, setShowBookmarksOnly] = useState<boolean>(false);
  const { copyState, copyToClipboard, hideCopyToast } = useCopyToClipboard();

  // 示例数据
  const categories: Category[] = [
    { id: 'all', name: '全部', icon: '🌐', count: 7 },
    { id: 'development', name: '开发工具', icon: '💻', count: 5 },
    { id: 'design', name: '设计资源', icon: '🎨', count: 1 },
    { id: 'productivity', name: '效率工具', icon: '⚡', count: 1 },
    { id: 'learning', name: '学习资源', icon: '📚', count: 0 },
    { id: 'entertainment', name: '娱乐休闲', icon: '🎮', count: 0 },
  ];

  const [websites, setWebsites] = useState<Website[]>([
    {
      id: '1',
      name: 'MDN Web Docs',
      url: 'https://developer.mozilla.org',
      description: '权威的Web开发文档',
      category: 'development',
      icon: '📖',
      tags: ['文档', 'Web', '标准'],
      isBookmarked: true,
    },
    {
      id: '2',
      name: 'Stack Overflow',
      url: 'https://stackoverflow.com',
      description: '程序员问答社区',
      category: 'development',
      icon: '📚',
      tags: ['问答', '编程', '社区'],
      isBookmarked: false,
    },
    {
      id: '3',
      name: 'Figma',
      url: 'https://figma.com',
      description: '在线协作设计工具',
      category: 'design',
      icon: '🎨',
      tags: ['设计', 'UI', '协作'],
      isBookmarked: true,
    },
    {
      id: '4',
      name: 'Notion',
      url: 'https://notion.so',
      description: '全能笔记和协作工具',
      category: 'productivity',
      icon: '📝',
      tags: ['笔记', '协作', '管理'],
      isBookmarked: false,
    },
    {
      id: '5',
      name: 'CodePen',
      url: 'https://codepen.io',
      description: '前端代码演示平台',
      category: 'development',
      icon: '✏️',
      tags: ['前端', '演示', '分享'],
      isBookmarked: false,
    },
    {
      id: '6',
      name: 'GitHub',
      url: 'https://github.com',
      description: '全球最大的代码托管平台',
      category: 'development',
      icon: '🐙',
      tags: ['代码', '开源', '协作'],
      isBookmarked: true,
    },
    {
      id: '7',
      name: 'Unix Time Help',
      url: 'https://unixtime.help',
      description: 'Unix时间戳转换和时间工具',
      category: 'development',
      icon: '🕒',
      tags: ['时间戳', 'Unix', '时间转换', '开发工具'],
      isBookmarked: true,
    },
  ]);

  // 过滤网站
  const filteredWebsites = websites.filter(website => {
    const matchesSearch = website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         website.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         website.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || website.category === selectedCategory;
    const matchesBookmark = !showBookmarksOnly || website.isBookmarked;
    
    return matchesSearch && matchesCategory && matchesBookmark;
  });

  const toggleBookmark = (websiteId: string) => {
    setWebsites(prev => prev.map(website => 
      website.id === websiteId 
        ? { ...website, isBookmarked: !website.isBookmarked }
        : website
    ));
  };

  const openWebsite = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">网站导航</h1>
          <p className="text-gray-600">
            精选优质网站，提升工作效率和学习体验
          </p>
        </div>

        {/* 搜索和过滤 */}
        <div className="mb-8 space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <input
              type="text"
              placeholder="搜索网站名称、描述或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              🔍
            </div>
          </div>

          {/* 分类过滤 */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.icon} {category.name} ({category.count})
              </button>
            ))}
          </div>

          {/* 收藏过滤 */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showBookmarksOnly}
                onChange={(e) => setShowBookmarksOnly(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">只显示收藏的网站</span>
            </label>
            <div className="text-sm text-gray-500">
              找到 {filteredWebsites.length} 个网站
            </div>
          </div>
        </div>

        {/* 网站列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWebsites.map((website) => (
            <Card key={website.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{website.icon}</div>
                    <div>
                      <CardTitle className="text-lg">{website.name}</CardTitle>
                      <p className="text-sm text-gray-500 mt-1">{website.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleBookmark(website.id)}
                    className={`text-xl transition-colors ${
                      website.isBookmarked ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-500'
                    }`}
                  >
                    {website.isBookmarked ? '⭐' : '☆'}
                  </button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* 标签 */}
                  <div className="flex flex-wrap gap-1">
                    {website.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => openWebsite(website.url)}
                      className="flex-1"
                      size="sm"
                    >
                      🔗 访问网站
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(website.url, `已复制: ${website.name}`)}
                    >
                      📋
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 空状态 */}
        {filteredWebsites.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">没有找到匹配的网站</h3>
            <p className="text-gray-500">
              尝试调整搜索条件或选择不同的分类
            </p>
          </div>
        )}

        {/* 添加网站按钮 */}
        <div className="fixed bottom-8 right-8">
          <Button
            size="lg"
            className="rounded-full shadow-lg"
            onClick={() => {
              // 这里可以打开添加网站的模态框
              alert('添加网站功能待实现');
            }}
          >
            ➕ 添加网站
          </Button>
        </div>
      </div>

      {/* 复制成功提示 */}
      <CopyToast
        show={copyState.show}
        message={copyState.message}
        onHide={hideCopyToast}
      />
    </div>
  );
};

export default NavigationPage;
