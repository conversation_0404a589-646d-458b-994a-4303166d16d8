import mongoose, { Document, Schema } from 'mongoose';

// 反馈文档接口
export interface IFeedback extends Document {
  type: 'bug' | 'feature' | 'improvement' | 'question' | 'other';
  title: string;
  content: string;
  email?: string;
  userId?: mongoose.Types.ObjectId;
  toolId?: string;
  status: 'pending' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  attachments?: Array<{
    filename: string;
    url: string;
    size: number;
    mimeType: string;
  }>;
  adminNotes?: string;
  adminReply?: string;
  adminReplyAt?: Date;
  resolvedAt?: Date;
  resolvedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// 使用统计文档接口
export interface IUsageStats extends Document {
  toolId: string;
  userId?: mongoose.Types.ObjectId;
  sessionId: string;
  action: 'view' | 'use' | 'download' | 'share' | 'favorite';
  metadata?: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    duration?: number; // 使用时长（毫秒）
    inputSize?: number; // 输入数据大小
    outputSize?: number; // 输出数据大小
  };
  createdAt: Date;
}

// 反馈附件 Schema
const feedbackAttachmentSchema = new Schema({
  filename: {
    type: String,
    required: true,
    trim: true,
  },
  url: {
    type: String,
    required: true,
    trim: true,
  },
  size: {
    type: Number,
    required: true,
    min: 0,
  },
  mimeType: {
    type: String,
    required: true,
    trim: true,
  },
}, { _id: false });

// 反馈 Schema
const feedbackSchema = new Schema<IFeedback>({
  type: {
    type: String,
    enum: ['bug', 'feature', 'improvement', 'question', 'other'],
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, '标题最多200个字符'],
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: [2000, '内容最多2000个字符'],
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址'],
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  toolId: {
    type: String,
    trim: true,
    default: null,
  },
  status: {
    type: String,
    enum: ['pending', 'in-progress', 'resolved', 'closed'],
    default: 'pending',
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签最多50个字符'],
  }],
  attachments: [feedbackAttachmentSchema],
  adminNotes: {
    type: String,
    trim: true,
    maxlength: [1000, '管理员备注最多1000个字符'],
  },
  adminReply: {
    type: String,
    trim: true,
    maxlength: [2000, '管理员回复最多2000个字符'],
  },
  adminReplyAt: {
    type: Date,
    default: null,
  },
  resolvedAt: {
    type: Date,
    default: null,
  },
  resolvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
}, {
  timestamps: true,
});

// 使用统计 Schema
const usageStatsSchema = new Schema<IUsageStats>({
  toolId: {
    type: String,
    required: true,
    trim: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  sessionId: {
    type: String,
    required: true,
    trim: true,
  },
  action: {
    type: String,
    enum: ['view', 'use', 'download', 'share', 'favorite'],
    required: true,
  },
  metadata: {
    userAgent: {
      type: String,
      trim: true,
    },
    ip: {
      type: String,
      trim: true,
    },
    referrer: {
      type: String,
      trim: true,
    },
    duration: {
      type: Number,
      min: 0,
    },
    inputSize: {
      type: Number,
      min: 0,
    },
    outputSize: {
      type: Number,
      min: 0,
    },
  },
}, {
  timestamps: { createdAt: true, updatedAt: false },
});

// 反馈索引
feedbackSchema.index({ status: 1, priority: -1, createdAt: -1 });
feedbackSchema.index({ type: 1, status: 1 });
feedbackSchema.index({ userId: 1, createdAt: -1 });
feedbackSchema.index({ toolId: 1, status: 1 });
feedbackSchema.index({ email: 1 });

// 使用统计索引
usageStatsSchema.index({ toolId: 1, createdAt: -1 });
usageStatsSchema.index({ userId: 1, createdAt: -1 });
usageStatsSchema.index({ sessionId: 1 });
usageStatsSchema.index({ action: 1, createdAt: -1 });

// 静态方法：获取工具使用统计
usageStatsSchema.statics.getToolStats = function(toolId: string, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        toolId,
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          action: '$action',
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: '$_id.date',
        actions: {
          $push: {
            action: '$_id.action',
            count: '$count',
          },
        },
        totalCount: { $sum: '$count' },
      },
    },
    {
      $sort: { _id: 1 },
    },
  ]);
};

// 静态方法：获取用户使用统计
usageStatsSchema.statics.getUserStats = function(userId: string, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: '$toolId',
        count: { $sum: 1 },
        lastUsed: { $max: '$createdAt' },
        actions: { $addToSet: '$action' },
      },
    },
    {
      $sort: { count: -1, lastUsed: -1 },
    },
  ]);
};

// 中间件：自动设置解决时间
feedbackSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'resolved' && !this.resolvedAt) {
    this.resolvedAt = new Date();
  }
  next();
});

// 创建模型
const Feedback = mongoose.models.Feedback || mongoose.model<IFeedback>('Feedback', feedbackSchema);
const UsageStats = mongoose.models.UsageStats || mongoose.model<IUsageStats>('UsageStats', usageStatsSchema);

export { Feedback, UsageStats };
export default Feedback;
