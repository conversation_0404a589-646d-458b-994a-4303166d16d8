'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from '@/components/tools/IOPanel';
import ShareButton from '@/components/share/ShareButton';

type EncodingType = 'url' | 'uri' | 'component';

const UrlEncoderPage: React.FC = () => {
  const [input, setInput] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [mode, setMode] = useState<'encode' | 'decode'>('encode');
  const [encodingType, setEncodingType] = useState<EncodingType>('component');
  const [error, setError] = useState<string>('');

  // 实时转换
  useEffect(() => {
    const timer = setTimeout(() => {
      if (input.trim()) {
        try {
          let result: string;
          if (mode === 'encode') {
            result = encodeText(input, encodingType);
          } else {
            result = decodeText(input, encodingType);
          }
          setOutput(result);
          setError('');
        } catch {
          setError(mode === 'encode' ? '编码失败' : '解码失败，请检查输入格式');
          setOutput('');
        }
      } else {
        setOutput('');
        setError('');
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [input, mode, encodingType]);

  const encodeText = (text: string, type: EncodingType): string => {
    switch (type) {
      case 'url':
        return encodeURI(text);
      case 'uri':
        return encodeURIComponent(text);
      case 'component':
        return encodeURIComponent(text);
      default:
        return encodeURIComponent(text);
    }
  };

  const decodeText = (text: string, type: EncodingType): string => {
    switch (type) {
      case 'url':
        return decodeURI(text);
      case 'uri':
        return decodeURIComponent(text);
      case 'component':
        return decodeURIComponent(text);
      default:
        return decodeURIComponent(text);
    }
  };

  const handleInputChange = (value: string) => {
    setInput(value);
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleExample = () => {
    if (mode === 'encode') {
      setInput('https://example.com/search?q=hello world&lang=zh-CN');
    } else {
      setInput('https%3A//example.com/search%3Fq%3Dhello%20world%26lang%3Dzh-CN');
    }
  };

  const handleSwap = () => {
    const newMode = mode === 'encode' ? 'decode' : 'encode';
    setMode(newMode);
    setInput(output);
    setOutput('');
  };

  const handleCopyOutput = async () => {
    if (output) {
      try {
        await navigator.clipboard.writeText(output);
        // 这里可以添加成功提示
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
    { label: '交换', icon: '🔄', onClick: handleSwap },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !output
    },
  ];

  const encodingTypes = [
    {
      value: 'component' as EncodingType,
      label: 'URI Component',
      description: '编码所有特殊字符，适用于URL参数'
    },
    {
      value: 'uri' as EncodingType,
      label: 'URI',
      description: '编码所有特殊字符，与Component相同'
    },
    {
      value: 'url' as EncodingType,
      label: 'URL',
      description: '保留URL结构字符，仅编码非ASCII字符'
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">URL编码解码工具</h1>
          <p className="text-gray-600">
            URL编码和解码工具，支持多种编码方式，处理URL中的特殊字符
          </p>
        </div>

        <div className="space-y-6">
          {/* 模式和类型选择 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>编码设置</CardTitle>
                {input && output && !error && (
                  <ShareButton
                    toolId="url-encoder"
                    toolName="URL 编码解码"
                    input={input}
                    output={output}
                    options={{
                      mode,
                      encodingType,
                      operation: mode === 'encode' ? '编码' : '解码',
                      inputLength: input.length,
                      outputLength: output.length,
                    }}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 模式选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    转换模式
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mode"
                        value="encode"
                        checked={mode === 'encode'}
                        onChange={(e) => setMode(e.target.value as 'encode' | 'decode')}
                        className="mr-2"
                      />
                      <span>编码 (文本 → URL编码)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mode"
                        value="decode"
                        checked={mode === 'decode'}
                        onChange={(e) => setMode(e.target.value as 'encode' | 'decode')}
                        className="mr-2"
                      />
                      <span>解码 (URL编码 → 文本)</span>
                    </label>
                  </div>
                </div>

                {/* 编码类型选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    编码类型
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {encodingTypes.map((type) => (
                      <div
                        key={type.value}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          encodingType === type.value
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setEncodingType(type.value)}
                      >
                        <div className="font-medium text-gray-900">{type.label}</div>
                        <div className="text-xs text-gray-500 mt-1">{type.description}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 输入输出面板 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入面板 */}
            <Card className="h-96">
              <IOPanel
                title={mode === 'encode' ? '文本输入' : 'URL编码输入'}
                placeholder={mode === 'encode' ? '请输入要编码的文本或URL...' : '请输入要解码的URL编码字符串...'}
                value={input}
                onChange={handleInputChange}
                actions={inputActions}
                maxLength={1048576}
                error={error}
              />
            </Card>

            {/* 输出面板 */}
            <Card className="h-96">
              <IOPanel
                title={mode === 'encode' ? 'URL编码输出' : '文本输出'}
                placeholder={mode === 'encode' ? 'URL编码结果将显示在这里...' : '解码后的文本将显示在这里...'}
                value={output}
                readonly
                actions={outputActions}
              />
            </Card>
          </div>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('encode');
                    setInput('https://example.com/search?q=hello world');
                  }}
                >
                  URL示例
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('encode');
                    setInput('你好世界 & 特殊字符!@#$%');
                  }}
                >
                  中文示例
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('decode');
                    setInput('https%3A//example.com/search%3Fq%3Dhello%20world');
                  }}
                >
                  解码示例
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSwap}
                  disabled={!output}
                >
                  🔄 交换输入输出
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 编码对比 */}
          <Card>
            <CardHeader>
              <CardTitle>编码方式对比</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">编码方式</th>
                      <th className="text-left py-2">保留字符</th>
                      <th className="text-left py-2">编码字符</th>
                      <th className="text-left py-2">适用场景</th>
                    </tr>
                  </thead>
                  <tbody className="text-gray-600">
                    <tr className="border-b">
                      <td className="py-2 font-medium">encodeURI</td>
                      <td className="py-2">: / ? # [ ] @</td>
                      <td className="py-2">空格、中文、特殊符号</td>
                      <td className="py-2">完整URL编码</td>
                    </tr>
                    <tr>
                      <td className="py-2 font-medium">encodeURIComponent</td>
                      <td className="py-2">A-Z a-z 0-9 - _ . ! ~ * &apos; ( )</td>
                      <td className="py-2">所有其他字符</td>
                      <td className="py-2">URL参数编码</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>URL编码:</strong> 将URL中的特殊字符转换为%XX格式，确保URL的正确传输</p>
                <p><strong>主要用途:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>URL参数传递中文和特殊字符</li>
                  <li>HTTP请求参数编码</li>
                  <li>表单数据提交</li>
                  <li>API接口参数处理</li>
                </ul>
                <p><strong>编码规则:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>ASCII字母和数字保持不变</li>
                  <li>空格编码为%20</li>
                  <li>中文字符按UTF-8编码后转换</li>
                  <li>特殊字符根据编码方式决定是否编码</li>
                </ul>
                <p><strong>注意事项:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>选择正确的编码方式很重要</li>
                  <li>完整URL使用encodeURI</li>
                  <li>URL参数使用encodeURIComponent</li>
                  <li>解码时要使用对应的解码方法</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UrlEncoderPage;
