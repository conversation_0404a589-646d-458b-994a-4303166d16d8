'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useUserPreferencesStore } from '@/store/userPreferencesStore';
import { TOOLS } from '@/lib/constants/tools';
import { User, Mail, Calendar, Shield, MessageSquare } from 'lucide-react';
import MyFeedback from '@/components/profile/MyFeedback';

interface UserStats {
  toolsUsed: number;
  totalUsage: number;
  favoriteTools: string[];
  joinDate: string;
}

// 移除未使用的接口定义

const ProfilePage: React.FC = () => {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'feedback' | 'settings'>('overview');

  // 使用用户偏好设置store
  const {
    preferences,
    stats,
    usageHistory,
    updatePreference,
    clearHistory,
    resetPreferences,
  } = useUserPreferencesStore();

  // 获取收藏工具的详细信息
  const favoriteToolsDetails = preferences.favoriteTools.map(toolId =>
    TOOLS.find(tool => tool.id === toolId)
  ).filter(Boolean);

  // 示例数据（保持兼容性）
  const userStats: UserStats = {
    toolsUsed: stats.totalToolsUsed,
    totalUsage: stats.totalUsageCount,
    favoriteTools: favoriteToolsDetails.map(tool => tool?.name || ''),
    joinDate: '2024-01-15',
  };

  // 移除未使用的示例数据

  // 移除本地状态，直接使用store中的偏好设置
  const handleSettingChange = (key: string, value: string | boolean | string[]) => {
    // 类型安全的更新偏好设置
    if (key === 'theme' && typeof value === 'string') {
      updatePreference('theme', value as 'light' | 'dark' | 'auto');
    } else if (key === 'language' && typeof value === 'string') {
      updatePreference('language', value as 'zh-CN' | 'en-US');
    } else if (key === 'fontSize' && typeof value === 'string') {
      updatePreference('fontSize', value as 'small' | 'medium' | 'large');
    } else if (key === 'notifications' && typeof value === 'boolean') {
      updatePreference('notifications', value);
    } else if (key === 'autoSave' && typeof value === 'boolean') {
      updatePreference('autoSave', value);
    } else if (key === 'soundEffects' && typeof value === 'boolean') {
      updatePreference('soundEffects', value);
    } else if (key === 'defaultTools' && Array.isArray(value)) {
      updatePreference('defaultTools', value);
    } else if (key === 'favoriteTools' && Array.isArray(value)) {
      updatePreference('favoriteTools', value);
    }
  };

  if (!session) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">请先登录</h1>
          <p className="text-gray-600 mb-6">
            您需要登录后才能查看个人中心
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            前往登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">个人中心</h1>
          <p className="text-gray-600">
            管理您的账户信息和使用偏好
          </p>
        </div>

        {/* 用户信息卡片 */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-2xl text-primary-600 font-bold">
                  {session.user?.name?.charAt(0) || session.user?.email?.charAt(0)}
                </span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  {session.user?.name || '用户'}
                </h2>
                <p className="text-gray-600">{session.user?.email}</p>
                <p className="text-sm text-gray-500">
                  加入时间: {userStats.joinDate}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: '概览', icon: '📊' },
                { id: 'history', label: '使用历史', icon: '📝' },
                { id: 'feedback', label: '我的反馈', icon: '💬' },
                { id: 'settings', label: '设置', icon: '⚙️' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'overview' | 'history' | 'feedback' | 'settings')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon} {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 标签页内容 */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">
                    {userStats.toolsUsed}
                  </div>
                  <div className="text-gray-600">使用过的工具</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {userStats.totalUsage}
                  </div>
                  <div className="text-gray-600">总使用次数</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {userStats.favoriteTools.length}
                  </div>
                  <div className="text-gray-600">收藏的工具</div>
                </CardContent>
              </Card>
            </div>

            {/* 常用工具 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>常用工具</CardTitle>
                  <Link href="/history">
                    <Button variant="outline" size="sm">
                      查看历史
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {favoriteToolsDetails.length > 0 ? (
                    favoriteToolsDetails.map((tool) => (
                      <div key={tool?.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{tool?.icon}</span>
                          <span className="font-medium">{tool?.name}</span>
                        </div>
                        <Link href={tool?.path || '#'}>
                          <Button variant="outline" size="sm">
                            快速访问
                          </Button>
                        </Link>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <div className="text-4xl mb-2">⭐</div>
                      <p>还没有收藏的工具</p>
                      <Link href="/tools">
                        <Button variant="outline" size="sm" className="mt-2">
                          浏览工具
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'history' && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>使用历史</CardTitle>
                <Link href="/history">
                  <Button variant="outline" size="sm">
                    查看全部
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {usageHistory.length > 0 ? (
                  usageHistory.slice(0, 5).map((item) => {
                    const tool = TOOLS.find(t => t.id === item.toolId);
                    return (
                      <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="text-2xl">{tool?.icon || '🔧'}</div>
                          <div>
                            <div className="font-medium">{item.toolName}</div>
                            <div className="text-sm text-gray-500">{item.action}</div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(item.usedAt).toLocaleDateString('zh-CN')}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <div className="text-4xl mb-2">📝</div>
                    <p>还没有使用记录</p>
                    <Link href="/tools">
                      <Button variant="outline" size="sm" className="mt-2">
                        开始使用工具
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'feedback' && (
          <MyFeedback />
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* 外观设置 */}
            <Card>
              <CardHeader>
                <CardTitle>外观设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    主题
                  </label>
                  <select
                    value={preferences.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="light">浅色主题</option>
                    <option value="dark">深色主题</option>
                    <option value="auto">跟随系统</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    语言
                  </label>
                  <select
                    value={preferences.language}
                    onChange={(e) => handleSettingChange('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="en-US">English</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* 功能设置 */}
            <Card>
              <CardHeader>
                <CardTitle>功能设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">通知提醒</div>
                    <div className="text-sm text-gray-500">接收系统通知和更新提醒</div>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.notifications}
                    onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                    className="h-4 w-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">自动保存</div>
                    <div className="text-sm text-gray-500">自动保存工具使用历史</div>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.autoSave}
                    onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                    className="h-4 w-4"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 保存按钮和重置 */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => {
                  if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
                    resetPreferences();
                    alert('设置已重置为默认值');
                  }
                }}
              >
                重置设置
              </Button>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => clearHistory()}
                >
                  清空历史
                </Button>
                <Button onClick={() => alert('设置已自动保存')}>
                  设置已保存
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePage;
