'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, Input } from '@/components/ui';
import { ToolComponentProps } from '@/types/tools';

const TimestampConverter: React.FC<ToolComponentProps> = ({
  onUsage
}) => {
  const [currentTimestamp, setCurrentTimestamp] = useState<number>(0);
  const [timestampInput, setTimestampInput] = useState<string>('');
  const [dateInput, setDateInput] = useState<string>('');
  const [isClient, setIsClient] = useState(false);

  // 客户端水合完成后初始化时间戳
  useEffect(() => {
    setIsClient(true);
    setCurrentTimestamp(Math.floor(Date.now() / 1000));

    const timer = setInterval(() => {
      setCurrentTimestamp(Math.floor(Date.now() / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 格式化时间戳为各种格式
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return {
      seconds: timestamp,
      milliseconds: timestamp * 1000,
      iso: date.toISOString(),
      local: date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      utc: date.toUTCString(),
      relative: getRelativeTime(date)
    };
  };

  // 获取相对时间
  const getRelativeTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} 天前`;
    if (hours > 0) return `${hours} 小时前`;
    if (minutes > 0) return `${minutes} 分钟前`;
    return `${seconds} 秒前`;
  };

  // 处理时间戳输入转换
  const handleTimestampConvert = () => {
    const timestamp = parseInt(timestampInput);
    if (!isNaN(timestamp)) {
      onUsage?.('use', { inputType: 'timestamp', value: timestamp });
    }
  };

  // 处理日期输入转换
  const handleDateConvert = () => {
    const date = new Date(dateInput);
    if (!isNaN(date.getTime())) {
      onUsage?.('use', { inputType: 'date', value: dateInput });
    }
  };

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const currentTime = isClient ? formatTimestamp(currentTimestamp) : {
    seconds: 0,
    milliseconds: 0,
    local: '加载中...',
    relative: '加载中...'
  };
  const timestampTime = timestampInput ? formatTimestamp(parseInt(timestampInput) || 0) : null;
  const dateTime = dateInput ? new Date(dateInput) : null;

  return (
    <div className="max-w-4xl space-y-4">
      {/* 页面标题 */}
      <h1 className="text-xl font-bold text-gray-900 mb-4">Unix 时间戳转换器</h1>

      {/* 当前时间戳 */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
              <span className="text-gray-600">当前时间戳（秒）</span>
              <div className="flex items-center space-x-2">
                <span className="font-mono">
                  {isClient ? currentTime.seconds : '加载中...'}
                </span>
                <Button
                  size="sm"
                  onClick={() => isClient && copyToClipboard(currentTime.seconds.toString())}
                  disabled={!isClient}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 text-xs"
                >
                  复制
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
              <span className="text-gray-600">当前时间戳（毫秒）</span>
              <div className="flex items-center space-x-2">
                <span className="font-mono">
                  {isClient ? currentTime.milliseconds : '加载中...'}
                </span>
                <Button
                  size="sm"
                  onClick={() => isClient && copyToClipboard(currentTime.milliseconds.toString())}
                  disabled={!isClient}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 text-xs"
                >
                  复制
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
              <span className="text-gray-600">当前时间</span>
              <div className="flex items-center space-x-2">
                <span className="font-mono">
                  {isClient ? currentTime.local : '加载中...'}
                </span>
                <Button
                  size="sm"
                  onClick={() => isClient && copyToClipboard(currentTime.local)}
                  disabled={!isClient}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 text-xs"
                >
                  复制
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 时间戳转换 */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-base font-semibold mb-3">时间戳转换</h3>

          <div className="flex space-x-2 mb-3">
            <Input
              id="timestamp-input"
              placeholder="1746200250"
              value={timestampInput}
              onChange={(e) => setTimestampInput(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={handleTimestampConvert}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              转换
            </Button>
          </div>

          {timestampTime && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-gray-600">格式化时间</span>
                <span className="font-mono">{timestampTime.local}</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-gray-600">相对时间</span>
                <span className="font-mono">{timestampTime.relative}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 日期转换 */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-base font-semibold mb-3">日期转换</h3>

          <div className="flex space-x-2 mb-3">
            <Input
              id="date-input"
              type="datetime-local"
              value={dateInput}
              onChange={(e) => setDateInput(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={handleDateConvert}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              转换
            </Button>
          </div>

          {dateTime && !isNaN(dateTime.getTime()) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-gray-600">时间戳（秒）</span>
                <span className="font-mono">{Math.floor(dateTime.getTime() / 1000)}</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="text-gray-600">时间戳（毫秒）</span>
                <span className="font-mono">{dateTime.getTime()}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TimestampConverter;
