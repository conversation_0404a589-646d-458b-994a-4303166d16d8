# Vercel 部署环境变量设置指南

## 🚨 紧急修复：NextAuth 500 错误

### 问题描述
- `GET /api/auth/session` 返回 500 错误
- NextAuth 提示服务器配置问题
- QR 码图片加载失败 (400 错误)

### 解决方案

#### 1. 设置 Vercel 环境变量

登录 [Vercel Dashboard](https://vercel.com/dashboard) → 选择项目 → Settings → Environment Variables

**必需设置的环境变量：**

```bash
# NextAuth 配置
NEXTAUTH_URL=https://cypress.fun
NEXTAUTH_SECRET=your-generated-secret-here

# MongoDB 配置
MONGODB_URI=your-mongodb-connection-string
MONGODB_DB=toollist

# 可选：调试模式（临时启用）
NEXTAUTH_DEBUG=true
```

#### 2. 生成 NEXTAUTH_SECRET

选择以下任一方法生成强密钥：

```bash
# 方法1: 使用 openssl
openssl rand -base64 32

# 方法2: 使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# 方法3: 在线生成
# 访问 https://generate-secret.vercel.app/32
```

#### 3. MongoDB 连接字符串格式

```bash
# MongoDB Atlas (推荐)
mongodb+srv://username:<EMAIL>/toollist?retryWrites=true&w=majority

# 自建 MongoDB
********************************:port/toollist
```

#### 4. 设置步骤

1. **NEXTAUTH_URL**
   - Name: `NEXTAUTH_URL`
   - Value: `https://cypress.fun`
   - Environment: Production, Preview, Development

2. **NEXTAUTH_SECRET**
   - Name: `NEXTAUTH_SECRET`
   - Value: [生成的32字符密钥]
   - Environment: Production, Preview, Development

3. **MONGODB_URI**
   - Name: `MONGODB_URI`
   - Value: [你的MongoDB连接字符串]
   - Environment: Production, Preview, Development

4. **MONGODB_DB**
   - Name: `MONGODB_DB`
   - Value: `toollist`
   - Environment: Production, Preview, Development

### 验证修复

设置完成后：

1. **自动重新部署**：Vercel 会自动重新部署
2. **手动重新部署**：在 Deployments 标签点击 "Redeploy"
3. **验证 API**：访问 `https://cypress.fun/api/auth/session`
4. **检查 QR 码**：访问 QR 码生成器工具

### 故障排除

如果仍有问题：

1. **检查函数日志**：Vercel Dashboard → Functions → 查看错误日志
2. **启用调试**：添加 `NEXTAUTH_DEBUG=true`
3. **检查环境变量**：访问 `/api/debug/env`（仅调试时）

### 演示账户

即使数据库连接失败，系统仍提供演示账户用于测试功能。
具体账户信息请联系开发者获取。
