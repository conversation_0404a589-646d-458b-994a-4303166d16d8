// 用户偏好设置类型定义

export interface UserPreferences {
  // 外观设置
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  fontSize: 'small' | 'medium' | 'large';

  // 功能设置
  autoSave: boolean;
  notifications: boolean;
  soundEffects: boolean;

  // 工具设置
  defaultTools: string[]; // 工具ID数组
  favoriteTools: string[]; // 收藏的工具ID
  recentTools: string[]; // 最近使用的工具ID

  // 隐私设置
  saveHistory: boolean;
  shareUsageData: boolean;

  // 界面设置
  compactMode: boolean;
  showToolTips: boolean;
  animationsEnabled: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  joinDate: Date;
  lastLoginDate: Date;
  preferences: UserPreferences;
}

export interface UserStats {
  totalToolsUsed: number;
  totalUsageCount: number;
  favoriteToolsCount: number;
  streakDays: number; // 连续使用天数
  lastActiveDate: Date;
}

export interface ToolUsageRecord {
  id: string;
  toolId: string;
  toolName: string;
  usedAt: Date;
  duration?: number; // 使用时长（秒）
  action: string; // 具体操作
  metadata?: Record<string, unknown>; // 额外数据
}

export interface UserActivity {
  id: string;
  userId: string;
  type: 'tool_usage' | 'login' | 'preference_change' | 'feedback_submit';
  description: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

// 默认用户偏好设置
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'auto',
  language: 'zh-CN',
  fontSize: 'medium',
  autoSave: true,
  notifications: true,
  soundEffects: false,
  defaultTools: ['timestamp-converter', 'json-formatter'],
  favoriteTools: [],
  recentTools: [],
  saveHistory: true,
  shareUsageData: false,
  compactMode: false,
  showToolTips: true,
  animationsEnabled: true,
};

// 用户偏好设置更新类型
export interface PreferenceUpdate {
  key: keyof UserPreferences;
  value: unknown;
}

// 工具使用统计
export interface ToolUsageStats {
  toolId: string;
  toolName: string;
  usageCount: number;
  totalDuration: number;
  lastUsed: Date;
  averageSessionTime: number;
}

// 用户成就系统
export interface UserAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt?: Date;
  progress: number; // 0-100
  maxProgress: number;
}

export const AVAILABLE_ACHIEVEMENTS: Omit<UserAchievement, 'unlockedAt' | 'progress'>[] = [
  {
    id: 'first_tool',
    name: '初次体验',
    description: '首次使用任意工具',
    icon: '🎯',
    maxProgress: 1,
  },
  {
    id: 'tool_explorer',
    name: '工具探索者',
    description: '使用过5个不同的工具',
    icon: '🔍',
    maxProgress: 5,
  },
  {
    id: 'power_user',
    name: '高级用户',
    description: '使用过所有工具',
    icon: '⚡',
    maxProgress: 12,
  },
  {
    id: 'daily_user',
    name: '每日用户',
    description: '连续7天使用工具',
    icon: '📅',
    maxProgress: 7,
  },
  {
    id: 'feedback_contributor',
    name: '反馈贡献者',
    description: '提交了第一个反馈',
    icon: '💬',
    maxProgress: 1,
  },
];
