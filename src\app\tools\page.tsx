'use client';

import React, { useState, useMemo, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Input } from '@/components/ui';
import { TOOLS, TOOL_CATEGORIES } from '@/lib/constants/tools';
import { useToolsStore } from '@/store';

function ToolsContent() {
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { favoriteTools, toggleFavorite } = useToolsStore();

  // 从URL参数中读取分类并设置默认过滤
  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam && TOOL_CATEGORIES.find(c => c.id === categoryParam)) {
      setSelectedCategory(categoryParam);
    }
  }, [searchParams]);

  // 过滤工具
  const filteredTools = useMemo(() => {
    let filtered = TOOLS;

    // 按分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query) ||
        tool.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [searchQuery, selectedCategory]);

  // 按分类分组工具
  const toolsByCategory = useMemo(() => {
    const grouped: Record<string, typeof TOOLS> = {};
    
    filteredTools.forEach(tool => {
      if (!grouped[tool.category]) {
        grouped[tool.category] = [];
      }
      grouped[tool.category].push(tool);
    });

    return grouped;
  }, [filteredTools]);

  const handleCategoryFilter = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  const handleToggleFavorite = (toolId: string) => {
    toggleFavorite(toolId);
  };

  return (
    <div className="container mx-auto py-8">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">开发工具集合</h1>
        <p className="text-lg text-gray-600 mb-6">
          精选的开发者工具，提升您的工作效率
        </p>

        {/* 搜索和过滤 */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          {/* 搜索框 */}
          <div className="flex-1">
            <Input
              placeholder="搜索工具..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
            />
          </div>

          {/* 分类过滤 */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === null ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleCategoryFilter(null)}
            >
              全部
            </Button>
            {TOOL_CATEGORIES.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleCategoryFilter(category.id)}
              >
                <span className="mr-1">{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>
            找到 {filteredTools.length} 个工具
            {selectedCategory && ` (${TOOL_CATEGORIES.find(c => c.id === selectedCategory)?.name})`}
          </span>
          <span>
            收藏 {favoriteTools.length} 个工具
          </span>
        </div>
      </div>

      {/* 工具列表 */}
      {Object.keys(toolsByCategory).length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的工具</h3>
          <p className="text-gray-500">尝试调整搜索关键词或选择不同的分类</p>
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(toolsByCategory).map(([categoryId, tools]) => {
            const category = TOOL_CATEGORIES.find(c => c.id === categoryId);
            if (!category) return null;

            return (
              <div key={categoryId}>
                {/* 分类标题 */}
                {!selectedCategory && (
                  <div className="flex items-center mb-4">
                    <span className="text-2xl mr-3">{category.icon}</span>
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">{category.name}</h2>
                      <p className="text-sm text-gray-500">{category.description}</p>
                    </div>
                  </div>
                )}

                {/* 工具网格 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {tools.map((tool) => (
                    <Card key={tool.id} variant="tool" className="group">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                              <span className="text-primary-600 text-lg">{tool.icon}</span>
                            </div>
                            <CardTitle className="group-hover:text-primary-600 transition-colors">
                              {tool.name}
                            </CardTitle>
                          </div>
                          
                          {/* 收藏按钮 */}
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              handleToggleFavorite(tool.id);
                            }}
                            className={`p-1 rounded-full transition-colors ${
                              favoriteTools.includes(tool.id)
                                ? 'text-yellow-500 hover:text-yellow-600'
                                : 'text-gray-400 hover:text-yellow-500'
                            }`}
                          >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          </button>
                        </div>
                      </CardHeader>
                      
                      <CardContent>
                        <CardDescription className="mb-4">{tool.description}</CardDescription>
                        
                        {/* 标签 */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {tool.tags.slice(0, 3).map((tag) => (
                            <span
                              key={tag}
                              className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                          {tool.tags.length > 3 && (
                            <span className="text-xs text-gray-400">
                              +{tool.tags.length - 3}
                            </span>
                          )}
                        </div>

                        {/* 工具信息 */}
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                          <span>访问 {tool.visitCount} 次</span>
                          {tool.requiredAuth && (
                            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                              需登录
                            </span>
                          )}
                        </div>

                        {/* 操作按钮 */}
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href={tool.path}>使用工具</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

// 加载组件
function ToolsLoading() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">开发工具集合</h1>
        <p className="text-lg text-gray-600 mb-6">
          精选的开发者工具，提升您的工作效率
        </p>
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="flex gap-2 mb-6">
            <div className="h-8 w-16 bg-gray-200 rounded"></div>
            <div className="h-8 w-20 bg-gray-200 rounded"></div>
            <div className="h-8 w-18 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-48 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

// 主要导出组件
export default function ToolsPage() {
  return (
    <Suspense fallback={<ToolsLoading />}>
      <ToolsContent />
    </Suspense>
  );
}
