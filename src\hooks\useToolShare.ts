import { useCallback } from 'react';
import { useToolUsage } from './useToolUsage';

interface UseToolShareProps {
  toolId: string;
  toolName: string;
}

export const useToolShare = ({ toolId, toolName }: UseToolShareProps) => {
  const { trackToolAction } = useToolUsage();

  const createShareData = useCallback((
    input: string,
    output?: string,
    options?: Record<string, unknown>
  ) => {
    // 记录分享操作
    trackToolAction(toolId, '分享工具状态');

    return {
      toolId,
      toolName,
      input,
      output,
      options: options || {},
    };
  }, [toolId, toolName, trackToolAction]);

  return {
    createShareData,
  };
};

// 常用工具的分享数据生成器
export const createToolShareData = {
  // Base64 编码工具
  base64: (input: string, output: string, isEncode: boolean) => ({
    toolId: 'base64-converter',
    toolName: 'Base64 编码解码',
    input,
    output,
    options: {
      operation: isEncode ? 'encode' : 'decode',
      inputLength: input.length,
      outputLength: output.length,
    },
  }),

  // 颜色转换工具
  color: (input: string, output: Record<string, string>) => ({
    toolId: 'color-converter',
    toolName: '颜色格式转换',
    input,
    output: JSON.stringify(output, null, 2),
    options: {
      formats: Object.keys(output),
      inputFormat: 'auto-detect',
    },
  }),

  // 大小写转换工具
  case: (input: string, output: string, caseType: string) => ({
    toolId: 'case-converter',
    toolName: '大小写转换',
    input,
    output,
    options: {
      caseType,
      wordCount: input.split(/\s+/).length,
      charCount: input.length,
    },
  }),

  // SHA 哈希工具
  hash: (input: string, output: string, algorithm: string) => ({
    toolId: 'sha-hash',
    toolName: 'SHA 哈希生成',
    input,
    output,
    options: {
      algorithm,
      inputLength: input.length,
      outputLength: output.length,
    },
  }),

  // URL 编码工具
  url: (input: string, output: string, isEncode: boolean) => ({
    toolId: 'url-encoder',
    toolName: 'URL 编码解码',
    input,
    output,
    options: {
      operation: isEncode ? 'encode' : 'decode',
      inputLength: input.length,
      outputLength: output.length,
    },
  }),

  // IP 转换工具
  ip: (input: string, output: Record<string, unknown>) => ({
    toolId: 'ip-converter',
    toolName: 'IP 地址转换',
    input,
    output: JSON.stringify(output, null, 2),
    options: {
      inputType: 'auto-detect',
      conversions: Object.keys(output),
    },
  }),

  // 二维码生成工具
  qr: (input: string, options: Record<string, unknown>) => ({
    toolId: 'qr-generator',
    toolName: '二维码生成器',
    input,
    output: '二维码图片已生成',
    options: {
      ...options,
      inputLength: input.length,
    },
  }),

  // 图片压缩工具
  imageCompress: (fileName: string, originalSize: number, compressedSize: number, options: Record<string, unknown>) => ({
    toolId: 'image-compressor',
    toolName: '图片压缩工具',
    input: `文件名: ${fileName}\n原始大小: ${(originalSize / 1024).toFixed(2)} KB`,
    output: `压缩后大小: ${(compressedSize / 1024).toFixed(2)} KB\n压缩率: ${((1 - compressedSize / originalSize) * 100).toFixed(1)}%`,
    options: {
      ...options,
      originalSize,
      compressedSize,
      compressionRatio: (1 - compressedSize / originalSize) * 100,
    },
  }),

  // 文本转换工具
  text: (input: string, output: string, operation: string) => ({
    toolId: 'text-converter',
    toolName: '文本转换工具',
    input,
    output,
    options: {
      operation,
      inputLength: input.length,
      outputLength: output.length,
      wordCount: input.split(/\s+/).length,
    },
  }),

  // MD5 哈希工具
  md5: (input: string, output: string) => ({
    toolId: 'md5-hash',
    toolName: 'MD5 哈希生成',
    input,
    output,
    options: {
      algorithm: 'MD5',
      inputLength: input.length,
      outputLength: output.length,
    },
  }),
};
