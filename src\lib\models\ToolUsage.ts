import mongoose, { Document, Schema } from 'mongoose';

// 工具使用统计接口
export interface IToolUsage extends Document {
  _id: string;
  toolId: string;
  userId?: string;
  sessionId: string;
  action: 'view' | 'use' | 'download' | 'share' | 'favorite' | 'copy';
  metadata?: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    duration?: number;
    inputSize?: number;
    outputSize?: number;
    errorMessage?: string;
    [key: string]: unknown;
  };
  createdAt: Date;
}

// 工具使用统计 Schema
const ToolUsageSchema = new Schema<IToolUsage>({
  toolId: {
    type: String,
    required: [true, '工具ID是必需的'],
    ref: 'Tool',
  },
  userId: {
    type: String,
    ref: 'User',
    default: null,
  },
  sessionId: {
    type: String,
    required: [true, '会话ID是必需的'],
    trim: true,
  },
  action: {
    type: String,
    required: [true, '操作类型是必需的'],
    enum: ['view', 'use', 'download', 'share', 'favorite', 'copy'],
  },
  metadata: {
    userAgent: {
      type: String,
      trim: true,
    },
    ip: {
      type: String,
      trim: true,
    },
    referrer: {
      type: String,
      trim: true,
    },
    duration: {
      type: Number,
      min: [0, '持续时间不能为负数'],
    },
    inputSize: {
      type: Number,
      min: [0, '输入大小不能为负数'],
    },
    outputSize: {
      type: Number,
      min: [0, '输出大小不能为负数'],
    },
    errorMessage: {
      type: String,
      trim: true,
    },
  },
}, {
  timestamps: { createdAt: true, updatedAt: false },
});

// 索引
ToolUsageSchema.index({ toolId: 1 });
ToolUsageSchema.index({ userId: 1 });
ToolUsageSchema.index({ sessionId: 1 });
ToolUsageSchema.index({ action: 1 });
ToolUsageSchema.index({ createdAt: 1 });
ToolUsageSchema.index({ toolId: 1, createdAt: 1 });
ToolUsageSchema.index({ userId: 1, createdAt: 1 });
ToolUsageSchema.index({ toolId: 1, action: 1 });

// 复合索引用于统计查询
ToolUsageSchema.index({
  toolId: 1,
  action: 1,
  createdAt: 1
});

// TTL 索引：30天后自动删除记录
ToolUsageSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// 静态方法：记录工具使用
ToolUsageSchema.statics.recordUsage = function(data: Partial<IToolUsage>) {
  return this.create({
    ...data,
    createdAt: new Date(),
  });
};

// 静态方法：获取工具使用统计
ToolUsageSchema.statics.getToolStats = function(toolId: string, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        toolId,
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: '$action',
        count: { $sum: 1 },
        uniqueUsers: { $addToSet: '$userId' },
        uniqueSessions: { $addToSet: '$sessionId' },
      },
    },
    {
      $project: {
        action: '$_id',
        count: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        uniqueSessions: { $size: '$uniqueSessions' },
        _id: 0,
      },
    },
  ]);
};

// 静态方法：获取每日使用统计
ToolUsageSchema.statics.getDailyStats = function(toolId: string, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        toolId,
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' },
        },
        totalUsage: { $sum: 1 },
        uniqueUsers: { $addToSet: '$userId' },
        uniqueSessions: { $addToSet: '$sessionId' },
        actions: { $push: '$action' },
      },
    },
    {
      $project: {
        date: {
          $dateFromParts: {
            year: '$_id.year',
            month: '$_id.month',
            day: '$_id.day',
          },
        },
        totalUsage: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        uniqueSessions: { $size: '$uniqueSessions' },
        actionCounts: {
          $reduce: {
            input: '$actions',
            initialValue: {},
            in: {
              $mergeObjects: [
                '$$value',
                {
                  $arrayToObject: [
                    [
                      {
                        k: '$$this',
                        v: {
                          $add: [
                            { $ifNull: [{ $getField: { field: '$$this', input: '$$value' } }, 0] },
                            1,
                          ],
                        },
                      },
                    ],
                  ],
                },
              ],
            },
          },
        },
        _id: 0,
      },
    },
    {
      $sort: { date: 1 },
    },
  ]);
};

// 静态方法：获取用户使用历史
ToolUsageSchema.statics.getUserHistory = function(userId: string, limit = 50) {
  return this.find({ userId })
    .populate('toolId', 'name icon path')
    .sort({ createdAt: -1 })
    .limit(limit);
};

// 静态方法：获取热门工具排行
ToolUsageSchema.statics.getPopularTools = function(days = 7, limit = 10) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        action: 'use',
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: '$toolId',
        usageCount: { $sum: 1 },
        uniqueUsers: { $addToSet: '$userId' },
        uniqueSessions: { $addToSet: '$sessionId' },
      },
    },
    {
      $project: {
        toolId: '$_id',
        usageCount: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        uniqueSessions: { $size: '$uniqueSessions' },
        _id: 0,
      },
    },
    {
      $sort: { usageCount: -1 },
    },
    {
      $limit: limit,
    },
    {
      $lookup: {
        from: 'tools',
        localField: 'toolId',
        foreignField: '_id',
        as: 'tool',
      },
    },
    {
      $unwind: '$tool',
    },
  ]);
};

// 静态方法：获取实时统计
ToolUsageSchema.statics.getRealTimeStats = function() {
  const oneHourAgo = new Date();
  oneHourAgo.setHours(oneHourAgo.getHours() - 1);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: oneHourAgo },
      },
    },
    {
      $group: {
        _id: null,
        totalActions: { $sum: 1 },
        uniqueUsers: { $addToSet: '$userId' },
        uniqueSessions: { $addToSet: '$sessionId' },
        uniqueTools: { $addToSet: '$toolId' },
        actionBreakdown: {
          $push: '$action',
        },
      },
    },
    {
      $project: {
        totalActions: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        uniqueSessions: { $size: '$uniqueSessions' },
        uniqueTools: { $size: '$uniqueTools' },
        actionCounts: {
          $reduce: {
            input: '$actionBreakdown',
            initialValue: {},
            in: {
              $mergeObjects: [
                '$$value',
                {
                  $arrayToObject: [
                    [
                      {
                        k: '$$this',
                        v: {
                          $add: [
                            { $ifNull: [{ $getField: { field: '$$this', input: '$$value' } }, 0] },
                            1,
                          ],
                        },
                      },
                    ],
                  ],
                },
              ],
            },
          },
        },
        _id: 0,
      },
    },
  ]);
};

// 导出模型
const ToolUsage = mongoose.models.ToolUsage || mongoose.model<IToolUsage>('ToolUsage', ToolUsageSchema);

export default ToolUsage;
