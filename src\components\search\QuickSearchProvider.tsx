'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import QuickSearchModal from './QuickSearchModal';

interface QuickSearchContextType {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
}

const QuickSearchContext = createContext<QuickSearchContextType | undefined>(undefined);

export const useQuickSearch = () => {
  const context = useContext(QuickSearchContext);
  if (!context) {
    throw new Error('useQuickSearch must be used within a QuickSearchProvider');
  }
  return context;
};

interface QuickSearchProviderProps {
  children: React.ReactNode;
}

const QuickSearchProvider: React.FC<QuickSearchProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen(prev => !prev);

  // 监听全局键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+K 或 Cmd+K (Mac)
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        toggle();
      }
      
      // 也支持 / 键快速搜索（类似GitHub）
      if (event.key === '/' && !isInputFocused()) {
        event.preventDefault();
        open();
      }
    };

    // 检查是否有输入框获得焦点
    const isInputFocused = () => {
      const activeElement = document.activeElement;
      return activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.getAttribute('contenteditable') === 'true'
      );
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const value: QuickSearchContextType = {
    isOpen,
    open,
    close,
    toggle,
  };

  return (
    <QuickSearchContext.Provider value={value}>
      {children}
      <QuickSearchModal isOpen={isOpen} onClose={close} />
    </QuickSearchContext.Provider>
  );
};

export default QuickSearchProvider;
