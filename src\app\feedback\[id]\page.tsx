import React from 'react';
import type { Metadata } from 'next';
import FeedbackDetailClient from './FeedbackDetailClient';

export const metadata: Metadata = {
  title: '反馈详情 - Tool List',
  description: '查看反馈详情和管理员回复',
};

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default async function FeedbackDetailPage({ params }: Props) {
  const { id } = await params;
  return <FeedbackDetailClient feedbackId={id} />;
}
