# Tool List - 详细设计文档

## 项目概览

### 项目定位

一个集成多种常用开发工具的在线平台，提供便捷的工具服务，支持用户权限管理和模块化扩展。

### 核心特性

- 模块化架构，易于扩展和维护
- 响应式设计，支持PC和移动端
- 用户权限管理，支持公开和私有工具
- RESTful API设计，模块间松耦合
- 现代化技术栈，性能优化

## 技术架构详细设计

### 技术选型详解

#### 前端技术栈

- **Next.js 14+**:
  - 使用App Router架构
  - 支持SSR/SSG/ISR多种渲染模式
  - 内置API Routes，简化后端开发
  - 自动代码分割和性能优化
- **TypeScript**: 类型安全，提升开发效率
- **Tailwind CSS**: 原子化CSS，快速样式开发
- **React Hook Form**: 表单管理和验证
- **Zustand**: 轻量级状态管理
- **React Query/TanStack Query**: 数据获取和缓存

#### 后端技术栈

- **Next.js API Routes**: 主要API服务
- **MongoDB**: 主数据库
- **Mongoose**: MongoDB ODM
- **NextAuth.js**: 身份认证
- **Golang**: 特定服务（如图片处理）

#### 部署和运维

- **Vercel**: 主应用部署（部署前运行npm run lint； 运行通过后， 在运行./push.bat推送到github，间接部署到vercel）
- **MongoDB Atlas**: 数据库托管
- **阿里云ECS**: Golang服务部署
- **Cloudinary/阿里云OSS**: 图片存储

## 项目代码结构设计

### 目录结构

```
toollist/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # 认证相关页面组
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── tools/             # 工具页面
│   │   │   ├── timestamp/
│   │   │   ├── json-formatter/
│   │   │   ├── text-converter/
│   │   │   └── ip-converter/
│   │   ├── admin/             # 管理后台
│   │   ├── api/               # API路由
│   │   │   ├── auth/
│   │   │   ├── tools/
│   │   │   └── admin/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/            # 可复用组件
│   │   ├── ui/               # 基础UI组件
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Modal.tsx
│   │   │   └── Card.tsx
│   │   ├── layout/           # 布局组件
│   │   │   ├── Header.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Footer.tsx
│   │   │   └── Navigation.tsx
│   │   ├── tools/            # 工具组件
│   │   │   ├── TimestampConverter.tsx
│   │   │   ├── JsonFormatter.tsx
│   │   │   └── TextConverter.tsx
│   │   └── common/           # 通用组件
│   │       ├── LoadingSpinner.tsx
│   │       ├── ErrorBoundary.tsx
│   │       └── ConfirmDialog.tsx
│   ├── lib/                  # 工具库和配置
│   │   ├── db/              # 数据库相关
│   │   │   ├── mongodb.ts
│   │   │   └── models/
│   │   │       ├── User.ts
│   │   │       ├── Tool.ts
│   │   │       └── Feedback.ts
│   │   ├── auth/            # 认证配置
│   │   │   └── config.ts
│   │   ├── utils/           # 工具函数
│   │   │   ├── validation.ts
│   │   │   ├── format.ts
│   │   │   └── api.ts
│   │   └── constants/       # 常量定义
│   │       ├── tools.ts
│   │       └── routes.ts
│   ├── hooks/               # 自定义Hooks
│   │   ├── useAuth.ts
│   │   ├── useTools.ts
│   │   └── useLocalStorage.ts
│   ├── store/               # 状态管理
│   │   ├── authStore.ts
│   │   ├── toolsStore.ts
│   │   └── uiStore.ts
│   └── types/               # TypeScript类型定义
│       ├── auth.ts
│       ├── tools.ts
│       └── api.ts
├── public/                  # 静态资源
│   ├── icons/
│   ├── images/
│   └── favicon.ico
├── docs/                    # 文档
├── tests/                   # 测试文件
│   ├── __tests__/
│   ├── __mocks__/
│   └── setup.ts
├── .env.local              # 环境变量
├── .env.example
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
└── README.md
```

### 模块化设计原则

#### 1. 工具模块结构

每个工具模块遵循统一的结构：

```typescript
// 工具模块接口
interface ToolModule {
  id: string;
  name: string;
  description: string;
  category: string;
  isPublic: boolean;
  requiredAuth: boolean;
  component: React.ComponentType;
  api?: {
    endpoint: string;
    methods: string[];
  };
}
```

#### 2. API设计模式

```typescript
// API路由结构
/api/tools/[toolId]/
├── GET     # 获取工具信息
├── POST    # 执行工具功能
├── PUT     # 更新工具配置
└── DELETE  # 删除工具（管理员）
```

## UI设计详细规范

### 设计系统

#### 色彩系统

```css
:root {
  /* 主色调 */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;

  /* 辅助色 */
  --secondary-500: #6b7280;
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-900: #111827;
}
```

#### 字体系统

```css
/* 字体大小 */
.text-xs {
  font-size: 0.75rem;
} /* 12px */
.text-sm {
  font-size: 0.875rem;
} /* 14px */
.text-base {
  font-size: 1rem;
} /* 16px */
.text-lg {
  font-size: 1.125rem;
} /* 18px */
.text-xl {
  font-size: 1.25rem;
} /* 20px */
.text-2xl {
  font-size: 1.5rem;
} /* 24px */
.text-3xl {
  font-size: 1.875rem;
} /* 30px */

/* 字重 */
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
```

#### 间距系统

```css
/* 基于8px网格系统 */
.space-1 {
  margin: 0.25rem;
} /* 4px */
.space-2 {
  margin: 0.5rem;
} /* 8px */
.space-3 {
  margin: 0.75rem;
} /* 12px */
.space-4 {
  margin: 1rem;
} /* 16px */
.space-6 {
  margin: 1.5rem;
} /* 24px */
.space-8 {
  margin: 2rem;
} /* 32px */
```

### 响应式设计

#### 断点系统

```css
/* 移动端优先 */
.container {
  width: 100%;
  padding: 0 1rem;
}

/* 平板 */
@media (min-width: 768px) {
  .container {
    max-width: 768px;
    margin: 0 auto;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

/* 大屏 */
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
```

#### 布局适配

- **移动端**: 单列布局，工具卡片垂直排列
- **平板**: 两列网格布局，侧边栏可折叠
- **桌面端**: 三列布局，固定侧边栏导航

### 页面布局设计

#### 1. 主页布局

```
┌─────────────────────────────────────┐
│ Header (Logo, Search, User Menu)    │
├─────────────────────────────────────┤
│ Hero Section                        │
│ - 欢迎信息                          │
│ - 快速搜索                          │
│ - 热门工具                          │
├─────────────────────────────────────┤
│ Tool Categories Grid                │
│ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐    │
│ │时间 │ │文本 │ │格式 │ │网络 │    │
│ │工具 │ │工具 │ │工具 │ │工具 │    │
│ └─────┘ └─────┘ └─────┘ └─────┘    │
├─────────────────────────────────────┤
│ Recent Tools / Favorites            │
├─────────────────────────────────────┤
│ Footer                              │
└─────────────────────────────────────┘
```

#### 2. 工具页面布局

```
┌─────────────────────────────────────┐
│ Header                              │
├─────────────────────────────────────┤
│ Breadcrumb Navigation               │
├─────────────────────────────────────┤
│ Tool Header                         │
│ - Tool Name & Description           │
│ - Action Buttons (Save, Share)      │
├─────────────────────────────────────┤
│ Tool Content Area                   │
│ ┌─────────────┐ ┌─────────────────┐ │
│ │ Input Panel │ │ Output Panel    │ │
│ │             │ │                 │ │
│ │             │ │                 │ │
│ └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│ Tool Options & Settings             │
├─────────────────────────────────────┤
│ Related Tools                       │
└─────────────────────────────────────┘
```

#### 3. 移动端布局适配

```
┌─────────────────┐
│ Header          │
│ ☰ Logo    👤    │
├─────────────────┤
│ Search Bar      │
├─────────────────┤
│ Tool Categories │
│ ┌─────────────┐ │
│ │ 时间工具    │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ 文本工具    │ │
│ └─────────────┘ │
├─────────────────┤
│ Recent Tools    │
├─────────────────┤
│ Footer          │
└─────────────────┘
```

### 组件设计规范

#### 1. 工具卡片组件

```typescript
interface ToolCardProps {
  tool: {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: string;
    isNew?: boolean;
    isFavorite?: boolean;
    lastUsed?: Date;
  };
  size?: 'small' | 'medium' | 'large';
  onClick: (toolId: string) => void;
  onFavorite?: (toolId: string) => void;
}

// 使用示例
<ToolCard
  tool={{
    id: 'timestamp-converter',
    name: 'Unix时间戳转换',
    description: '在Unix时间戳和标准时间格式之间进行转换',
    icon: 'clock',
    category: 'time',
    isNew: true
  }}
  size="medium"
  onClick={handleToolClick}
  onFavorite={handleFavorite}
/>
```

#### 2. 输入输出面板组件

```typescript
interface IOPanelProps {
  title: string;
  placeholder?: string;
  value: string;
  onChange?: (value: string) => void;
  readonly?: boolean;
  language?: string; // 用于语法高亮
  actions?: Array<{
    label: string;
    icon: string;
    onClick: () => void;
    disabled?: boolean;
  }>;
  maxLength?: number;
  showLineNumbers?: boolean;
}

// 使用示例
<IOPanel
  title="输入"
  placeholder="请输入要转换的时间戳..."
  value={inputValue}
  onChange={setInputValue}
  actions={[
    { label: '清空', icon: 'trash', onClick: clearInput },
    { label: '示例', icon: 'lightbulb', onClick: loadExample }
  ]}
/>
```

#### 3. 导航组件

```typescript
interface NavigationProps {
  items: Array<{
    id: string;
    label: string;
    icon: string;
    href: string;
    badge?: number;
    children?: NavigationItem[];
  }>;
  activeItem?: string;
  collapsed?: boolean;
  onToggle?: () => void;
}
```

### 交互设计详细规范

#### 1. 微交互动画

```css
/* 按钮悬停效果 */
.btn {
  transition: all 0.2s ease-in-out;
  transform: translateY(0);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
  transition: all 0.1s ease-in-out;
}

/* 工具卡片动画 */
.tool-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
}

.tool-card:hover {
  transform: scale(1.02) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 输入框聚焦动画 */
.input-field {
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;
}

.input-field:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 2. 页面切换动画

```typescript
// 使用Framer Motion实现页面切换
const pageVariants = {
  initial: { opacity: 0, x: -20 },
  in: { opacity: 1, x: 0 },
  out: { opacity: 0, x: 20 },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};
```

#### 3. 加载状态设计

```typescript
// 骨架屏组件
const ToolCardSkeleton = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
    <div className="h-8 bg-gray-200 rounded"></div>
  </div>
);

// 进度指示器
const ProgressIndicator = ({ progress }: { progress: number }) => (
  <div className="w-full bg-gray-200 rounded-full h-2">
    <div
      className="bg-primary-500 h-2 rounded-full transition-all duration-300"
      style={{ width: `${progress}%` }}
    />
  </div>
);
```

#### 4. 反馈机制设计

```typescript
// 通知系统
interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    onClick: () => void;
  }>;
}

// Toast组件
const Toast = ({ type, title, message, duration = 5000 }: NotificationProps) => {
  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  };

  return (
    <div className={`toast toast-${type}`}>
      <div className="toast-icon">{icons[type]}</div>
      <div className="toast-content">
        <h4>{title}</h4>
        <p>{message}</p>
      </div>
    </div>
  );
};
```

### 可访问性设计

#### 1. 键盘导航

```typescript
// 键盘快捷键配置
const keyboardShortcuts = {
  'Ctrl+K': '打开搜索',
  'Ctrl+/': '显示快捷键帮助',
  Escape: '关闭模态框',
  Tab: '下一个焦点元素',
  'Shift+Tab': '上一个焦点元素',
  Enter: '激活当前元素',
  Space: '选择/取消选择',
};

// 焦点管理Hook
const useFocusManagement = () => {
  const focusableElements =
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

  const trapFocus = (container: HTMLElement) => {
    const focusable = container.querySelectorAll(focusableElements);
    const firstFocusable = focusable[0] as HTMLElement;
    const lastFocusable = focusable[focusable.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusable) {
            lastFocusable.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusable) {
            firstFocusable.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  };

  return { trapFocus };
};
```

#### 2. 屏幕阅读器支持

```typescript
// ARIA标签组件
const AriaLabel = ({
  children,
  label,
  describedBy,
  role
}: {
  children: React.ReactNode;
  label?: string;
  describedBy?: string;
  role?: string;
}) => (
  <div
    aria-label={label}
    aria-describedby={describedBy}
    role={role}
  >
    {children}
  </div>
);

// 语义化按钮组件
const AccessibleButton = ({
  children,
  onClick,
  disabled,
  ariaLabel,
  ariaPressed
}: {
  children: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  ariaLabel?: string;
  ariaPressed?: boolean;
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    aria-label={ariaLabel}
    aria-pressed={ariaPressed}
    className="focus:outline-none focus:ring-2 focus:ring-primary-500"
  >
    {children}
  </button>
);
```

#### 3. 色彩对比和主题

```css
/* 高对比度主题 */
.theme-high-contrast {
  --primary-500: #000000;
  --background: #ffffff;
  --text-primary: #000000;
  --text-secondary: #333333;
  --border: #000000;
}

/* 深色主题 */
.theme-dark {
  --primary-500: #60a5fa;
  --background: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --border: #374151;
}

/* 确保对比度符合WCAG标准 */
.text-contrast-aa {
  color: contrast(var(--background), #000000, #ffffff, 4.5);
}

.text-contrast-aaa {
  color: contrast(var(--background), #000000, #ffffff, 7);
}
```

## 模块详细设计

### 1. 登录认证模块

#### 数据模型设计

```typescript
// 用户模型
interface User {
  id: string;
  email: string;
  username: string;
  avatar?: string;
  role: 'user' | 'admin' | 'premium';
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: 'zh-CN' | 'en-US';
    favoriteTools: string[];
    recentTools: Array<{
      toolId: string;
      lastUsed: Date;
    }>;
  };
  subscription?: {
    plan: 'free' | 'pro' | 'enterprise';
    expiresAt: Date;
    features: string[];
  };
  createdAt: Date;
  lastLoginAt: Date;
  isEmailVerified: boolean;
  twoFactorEnabled: boolean;
}

// 会话模型
interface Session {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  deviceInfo: {
    userAgent: string;
    ip: string;
    location?: string;
  };
  createdAt: Date;
}
```

#### API设计

```typescript
// 认证API接口
interface AuthAPI {
  // 用户注册
  register(data: {
    email: string;
    username: string;
    password: string;
  }): Promise<{ user: User; token: string }>;

  // 用户登录
  login(data: {
    email: string;
    password: string;
    rememberMe?: boolean;
  }): Promise<{ user: User; token: string; refreshToken: string }>;

  // 第三方登录
  oauthLogin(
    provider: 'github' | 'google',
    code: string
  ): Promise<{ user: User; token: string }>;

  // 刷新Token
  refreshToken(
    refreshToken: string
  ): Promise<{ token: string; refreshToken: string }>;

  // 登出
  logout(): Promise<void>;

  // 密码重置
  resetPassword(email: string): Promise<void>;
  confirmResetPassword(token: string, newPassword: string): Promise<void>;
}
```

### 2. 时间转换工具模块

#### 功能特性详细设计

```typescript
interface TimestampTool {
  // 时间戳转换
  convertTimestamp(input: {
    value: number | string;
    fromFormat: 'unix' | 'milliseconds' | 'iso' | 'custom';
    toFormat: 'unix' | 'milliseconds' | 'iso' | 'custom';
    timezone?: string;
    customFormat?: string;
  }): {
    result: string;
    formatted: {
      local: string;
      utc: string;
      iso: string;
      unix: number;
      milliseconds: number;
    };
  };

  // 时间差计算
  calculateDifference(
    start: Date,
    end: Date
  ): {
    years: number;
    months: number;
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    totalMilliseconds: number;
    humanReadable: string;
  };

  // 时区转换
  convertTimezone(
    date: Date,
    fromTz: string,
    toTz: string
  ): {
    original: string;
    converted: string;
    offset: number;
  };

  // 批量转换
  batchConvert(
    inputs: string[],
    format: string
  ): Array<{
    input: string;
    output: string;
    error?: string;
  }>;
}
```

#### 组件实现

```typescript
const TimestampConverter = () => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [format, setFormat] = useState<'unix' | 'iso' | 'custom'>('unix');
  const [timezone, setTimezone] = useState('UTC');

  const convert = useCallback((value: string) => {
    try {
      const result = timestampTool.convertTimestamp({
        value,
        fromFormat: 'auto',
        toFormat: format,
        timezone
      });
      setOutput(JSON.stringify(result.formatted, null, 2));
    } catch (error) {
      setOutput(`错误: ${error.message}`);
    }
  }, [format, timezone]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <IOPanel
        title="输入时间"
        value={input}
        onChange={(value) => {
          setInput(value);
          convert(value);
        }}
        placeholder="输入时间戳、ISO时间或自然语言时间..."
        actions={[
          { label: '当前时间', icon: 'clock', onClick: () => setInput(Date.now().toString()) },
          { label: '清空', icon: 'trash', onClick: () => setInput('') }
        ]}
      />

      <IOPanel
        title="转换结果"
        value={output}
        readonly
        language="json"
        actions={[
          { label: '复制', icon: 'copy', onClick: () => navigator.clipboard.writeText(output) },
          { label: '下载', icon: 'download', onClick: () => downloadAsFile(output, 'timestamp.json') }
        ]}
      />

      <div className="lg:col-span-2">
        <ToolOptions>
          <Select label="输出格式" value={format} onChange={setFormat}>
            <option value="unix">Unix时间戳</option>
            <option value="iso">ISO 8601</option>
            <option value="custom">自定义格式</option>
          </Select>

          <Select label="时区" value={timezone} onChange={setTimezone}>
            <option value="UTC">UTC</option>
            <option value="Asia/Shanghai">北京时间</option>
            <option value="America/New_York">纽约时间</option>
          </Select>
        </ToolOptions>
      </div>
    </div>
  );
};
```

### 3. JSON格式化工具模块

#### 功能特性

```typescript
interface JsonTool {
  // 格式化JSON
  format(
    json: string,
    options: {
      indent: number;
      sortKeys: boolean;
      removeComments: boolean;
    }
  ): {
    formatted: string;
    isValid: boolean;
    errors: string[];
    stats: {
      size: number;
      lines: number;
      objects: number;
      arrays: number;
    };
  };

  // 压缩JSON
  minify(json: string): {
    minified: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  };

  // JSON路径查询
  query(
    json: object,
    path: string
  ): {
    result: any;
    path: string[];
    type: string;
  };

  // JSON Schema验证
  validate(
    json: string,
    schema: object
  ): {
    isValid: boolean;
    errors: Array<{
      path: string;
      message: string;
      value: any;
    }>;
  };
}
```

### 4. 文本转换工具模块

#### 功能特性

```typescript
interface TextTool {
  // 大小写转换
  convertCase(
    text: string,
    type: 'upper' | 'lower' | 'title' | 'sentence'
  ): string;

  // 命名格式转换
  convertNaming(
    text: string,
    from: string,
    to:
      | 'camelCase'
      | 'PascalCase'
      | 'snake_case'
      | 'kebab-case'
      | 'CONSTANT_CASE'
  ): string;

  // 编码转换
  encode(text: string, type: 'base64' | 'url' | 'html' | 'unicode'): string;
  decode(text: string, type: 'base64' | 'url' | 'html' | 'unicode'): string;

  // 文本统计
  analyze(text: string): {
    characters: number;
    charactersNoSpaces: number;
    words: number;
    sentences: number;
    paragraphs: number;
    readingTime: number; // 分钟
    complexity: 'easy' | 'medium' | 'hard';
  };

  // 文本处理
  process(
    text: string,
    operations: Array<{
      type:
        | 'trim'
        | 'removeEmptyLines'
        | 'removeDuplicateLines'
        | 'sort'
        | 'reverse';
      options?: any;
    }>
  ): string;
}
```

### 5. 导航网站模块

#### 数据模型

```typescript
interface Website {
  id: string;
  name: string;
  url: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  addedBy: string;
  visitCount: number;
  lastVisited?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  parentId?: string;
  order: number;
  isPublic: boolean;
}
```

## 性能优化策略

### 1. 前端性能优化

#### 代码分割和懒加载

```typescript
// 路由级别代码分割
const TimestampConverter = lazy(
  () => import('@/components/tools/TimestampConverter')
);
const JsonFormatter = lazy(() => import('@/components/tools/JsonFormatter'));

// 组件懒加载
const LazyToolCard = lazy(() => import('@/components/ui/ToolCard'));

// 动态导入工具模块
const loadTool = async (toolId: string) => {
  const toolModule = await import(`@/tools/${toolId}`);
  return toolModule.default;
};
```

#### 图片优化

```typescript
// Next.js Image组件配置
const optimizedImageConfig = {
  domains: ['example.com'],
  formats: ['image/webp', 'image/avif'],
  sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality: 85,
  placeholder: 'blur',
  blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...'
};

// 响应式图片组件
const ResponsiveImage = ({ src, alt, ...props }) => (
  <Image
    src={src}
    alt={alt}
    {...optimizedImageConfig}
    {...props}
  />
);
```

#### 缓存策略

```typescript
// React Query缓存配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

// 工具结果缓存
const useToolCache = () => {
  const cache = useRef(new Map());

  const getCachedResult = (toolId: string, input: string) => {
    const key = `${toolId}:${hashInput(input)}`;
    return cache.current.get(key);
  };

  const setCachedResult = (toolId: string, input: string, result: any) => {
    const key = `${toolId}:${hashInput(input)}`;
    cache.current.set(key, result);

    // 限制缓存大小
    if (cache.current.size > 100) {
      const firstKey = cache.current.keys().next().value;
      cache.current.delete(firstKey);
    }
  };

  return { getCachedResult, setCachedResult };
};
```

### 2. 后端性能优化

#### 数据库优化

```typescript
// MongoDB索引策略
const userIndexes = [
  { email: 1 }, // 唯一索引
  { username: 1 }, // 唯一索引
  { 'preferences.favoriteTools': 1 }, // 多键索引
  { createdAt: -1 }, // 时间排序
  { role: 1, isEmailVerified: 1 }, // 复合索引
];

const toolIndexes = [
  { category: 1, isPublic: 1 }, // 分类查询
  { tags: 1 }, // 标签搜索
  { name: 'text', description: 'text' }, // 全文搜索
  { visitCount: -1 }, // 热门排序
  { createdAt: -1 }, // 最新排序
];

// 查询优化
const findToolsOptimized = async (filters: {
  category?: string;
  tags?: string[];
  search?: string;
  limit?: number;
  offset?: number;
}) => {
  const pipeline = [];

  // 匹配阶段
  const matchStage: any = { isPublic: true };
  if (filters.category) matchStage.category = filters.category;
  if (filters.tags?.length) matchStage.tags = { $in: filters.tags };
  if (filters.search) {
    matchStage.$text = { $search: filters.search };
  }
  pipeline.push({ $match: matchStage });

  // 排序阶段
  if (filters.search) {
    pipeline.push({ $sort: { score: { $meta: 'textScore' }, visitCount: -1 } });
  } else {
    pipeline.push({ $sort: { visitCount: -1, createdAt: -1 } });
  }

  // 分页阶段
  if (filters.offset) pipeline.push({ $skip: filters.offset });
  if (filters.limit) pipeline.push({ $limit: filters.limit });

  return Tool.aggregate(pipeline);
};
```

#### API响应优化

```typescript
// 响应压缩
app.use(
  compression({
    level: 6,
    threshold: 1024,
    filter: (req, res) => {
      if (req.headers['x-no-compression']) return false;
      return compression.filter(req, res);
    },
  })
);

// 响应缓存
const cacheMiddleware =
  (duration: number) => (req: Request, res: Response, next: NextFunction) => {
    res.set('Cache-Control', `public, max-age=${duration}`);
    next();
  };

// API限流
const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 100次请求
  message: '请求过于频繁，请稍后再试',
  standardHeaders: true,
  legacyHeaders: false,
});
```

## 安全考虑

### 1. 身份认证和授权

#### JWT安全配置

```typescript
const jwtConfig = {
  secret: process.env.JWT_SECRET,
  expiresIn: '15m', // 短期访问令牌
  refreshExpiresIn: '7d', // 长期刷新令牌
  algorithm: 'HS256',
  issuer: 'toollist.app',
  audience: 'toollist.app',
};

// Token验证中间件
const verifyToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) throw new Error('Token missing');

    const decoded = jwt.verify(token, jwtConfig.secret) as JwtPayload;
    const user = await User.findById(decoded.userId);
    if (!user) throw new Error('User not found');

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Unauthorized' });
  }
};
```

### 2. 数据验证和清理

#### 输入验证

```typescript
// 使用Zod进行数据验证
const userRegistrationSchema = z.object({
  email: z.string().email('无效的邮箱格式'),
  username: z
    .string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符'),
  password: z
    .string()
    .min(8, '密码至少8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
});

const toolInputSchema = z.object({
  toolId: z.string().uuid('无效的工具ID'),
  input: z.string().max(1000000, '输入内容过长'), // 1MB限制
  options: z.record(z.any()).optional(),
});

// XSS防护
const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
  });
};
```

### 3. API安全

#### CORS配置

```typescript
const corsOptions = {
  origin:
    process.env.NODE_ENV === 'production'
      ? ['https://toollist.app', 'https://www.toollist.app']
      : ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
};
```

#### 安全头设置

```typescript
// 安全中间件
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        imgSrc: ["'self'", 'data:', 'https:'],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", 'https://api.toollist.app'],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  })
);
```

## 测试策略

### 1. 单元测试

#### 工具函数测试

```typescript
// 时间戳转换工具测试
describe('TimestampTool', () => {
  const tool = new TimestampTool();

  test('should convert unix timestamp to ISO format', () => {
    const result = tool.convertTimestamp({
      value: 1640995200,
      fromFormat: 'unix',
      toFormat: 'iso',
    });

    expect(result.result).toBe('2022-01-01T00:00:00.000Z');
    expect(result.formatted.iso).toBe('2022-01-01T00:00:00.000Z');
  });

  test('should handle invalid input gracefully', () => {
    expect(() => {
      tool.convertTimestamp({
        value: 'invalid',
        fromFormat: 'unix',
        toFormat: 'iso',
      });
    }).toThrow('Invalid timestamp format');
  });
});
```

#### 组件测试

```typescript
// 工具卡片组件测试
describe('ToolCard', () => {
  const mockTool = {
    id: 'test-tool',
    name: 'Test Tool',
    description: 'A test tool',
    icon: 'test-icon',
    category: 'test'
  };

  test('should render tool information correctly', () => {
    render(<ToolCard tool={mockTool} onClick={jest.fn()} />);

    expect(screen.getByText('Test Tool')).toBeInTheDocument();
    expect(screen.getByText('A test tool')).toBeInTheDocument();
  });

  test('should call onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<ToolCard tool={mockTool} onClick={handleClick} />);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledWith('test-tool');
  });
});
```

### 2. 集成测试

#### API集成测试

```typescript
describe('Auth API', () => {
  test('should register new user successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      password: 'TestPass123',
    };

    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    expect(response.body.user.email).toBe(userData.email);
    expect(response.body.token).toBeDefined();
  });

  test('should reject duplicate email registration', async () => {
    // 先注册一个用户
    await User.create({
      email: '<EMAIL>',
      username: 'existing',
      password: 'hashedpassword',
    });

    const response = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        username: 'newuser',
        password: 'TestPass123',
      })
      .expect(400);

    expect(response.body.error).toContain('邮箱已存在');
  });
});
```

### 3. E2E测试

#### 用户流程测试

```typescript
// 使用Playwright进行E2E测试
describe('User Journey', () => {
  test('should complete full tool usage flow', async ({ page }) => {
    // 访问首页
    await page.goto('/');
    await expect(page.locator('h1')).toContainText('Tool List');

    // 点击时间戳转换工具
    await page.click('[data-testid="timestamp-converter"]');
    await expect(page).toHaveURL('/tools/timestamp');

    // 输入时间戳
    await page.fill('[data-testid="input-panel"]', '1640995200');

    // 验证输出结果
    const output = page.locator('[data-testid="output-panel"]');
    await expect(output).toContainText('2022-01-01');

    // 复制结果
    await page.click('[data-testid="copy-button"]');

    // 验证复制成功提示
    await expect(page.locator('.toast')).toContainText('复制成功');
  });

  test('should work on mobile devices', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/');

    // 验证移动端布局
    const header = page.locator('header');
    await expect(header).toBeVisible();

    // 测试移动端导航
    await page.click('[data-testid="mobile-menu-toggle"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
  });
});
```

## 部署和运维

### 1. CI/CD流程

#### GitHub Actions配置

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - run: npm ci
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

### 2. 监控和日志

#### 错误监控

```typescript
// 使用Sentry进行错误监控
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  beforeSend(event) {
    // 过滤敏感信息
    if (event.user) {
      delete event.user.email;
    }
    return event;
  },
});

// 性能监控
const performanceMonitor = {
  trackPageLoad: (pageName: string) => {
    const startTime = performance.now();
    return () => {
      const loadTime = performance.now() - startTime;
      analytics.track('Page Load', {
        page: pageName,
        loadTime: Math.round(loadTime),
      });
    };
  },

  trackToolUsage: (toolId: string, executionTime: number) => {
    analytics.track('Tool Used', {
      toolId,
      executionTime,
      timestamp: new Date().toISOString(),
    });
  },
};
```

### 3. 备份和恢复

#### 数据库备份策略

```bash
#!/bin/bash
# MongoDB备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mongodb"
DB_NAME="toollist"

# 创建备份
mongodump --host localhost:27017 --db $DB_NAME --out $BACKUP_DIR/$DATE

# 压缩备份文件
tar -czf $BACKUP_DIR/$DATE.tar.gz -C $BACKUP_DIR $DATE

# 删除原始备份目录
rm -rf $BACKUP_DIR/$DATE

# 保留最近30天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

# 上传到云存储
aws s3 cp $BACKUP_DIR/$DATE.tar.gz s3://toollist-backups/mongodb/
```

这个详细的设计文档涵盖了Tool List项目的所有重要方面，从技术架构到UI设计，从模块实现到性能优化，为项目的开发提供了全面的指导。
