'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from '@/components/tools/IOPanel';
import ShareButton from '@/components/share/ShareButton';

type HashType = 'SHA-1' | 'SHA-256' | 'SHA-384' | 'SHA-512';

const ShaHashPage: React.FC = () => {
  const [input, setInput] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [hashType, setHashType] = useState<HashType>('SHA-256');
  const [error, setError] = useState<string>('');
  const [isComputing, setIsComputing] = useState<boolean>(false);

  // 实时计算哈希
  useEffect(() => {
    const timer = setTimeout(async () => {
      if (input.trim()) {
        setIsComputing(true);
        try {
          const hash = await generateHash(input, hashType);
          setOutput(hash);
          setError('');
        } catch {
          setError('计算哈希失败');
          setOutput('');
        } finally {
          setIsComputing(false);
        }
      } else {
        setOutput('');
        setError('');
        setIsComputing(false);
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [input, hashType]);

  // SHA哈希计算
  const generateHash = async (text: string, type: HashType): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);

    let algorithm: string;
    switch (type) {
      case 'SHA-1':
        algorithm = 'SHA-1';
        break;
      case 'SHA-256':
        algorithm = 'SHA-256';
        break;
      case 'SHA-384':
        algorithm = 'SHA-384';
        break;
      case 'SHA-512':
        algorithm = 'SHA-512';
        break;
      default:
        algorithm = 'SHA-256';
    }

    const hashBuffer = await crypto.subtle.digest(algorithm, data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
  };

  const handleInputChange = (value: string) => {
    setInput(value);
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleExample = () => {
    setInput('Hello World');
  };

  const handleCopyOutput = async () => {
    if (output) {
      try {
        await navigator.clipboard.writeText(output);
        // 这里可以添加成功提示
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !output
    },
  ];

  const hashTypes: { value: HashType; label: string; description: string }[] = [
    { value: 'SHA-1', label: 'SHA-1', description: '160位哈希值，已不推荐用于安全场景' },
    { value: 'SHA-256', label: 'SHA-256', description: '256位哈希值，广泛使用的安全算法' },
    { value: 'SHA-384', label: 'SHA-384', description: '384位哈希值，更高安全性' },
    { value: 'SHA-512', label: 'SHA-512', description: '512位哈希值，最高安全性' },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">SHA哈希计算工具</h1>
          <p className="text-gray-600">
            计算文本的SHA系列哈希值，支持SHA-1、SHA-256、SHA-384、SHA-512算法
          </p>
        </div>

        <div className="space-y-6">
          {/* 算法选择 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>哈希算法选择</CardTitle>
                {input && output && !error && (
                  <ShareButton
                    toolId="sha-hash"
                    toolName="SHA 哈希生成"
                    input={input}
                    output={output}
                    options={{
                      algorithm: hashType,
                      inputLength: input.length,
                      outputLength: output.length,
                      hashBits: hashType.replace('SHA-', ''),
                    }}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {hashTypes.map((type) => (
                  <div
                    key={type.value}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      hashType === type.value
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setHashType(type.value)}
                  >
                    <div className="font-medium text-gray-900">{type.label}</div>
                    <div className="text-sm text-gray-500 mt-1">{type.description}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 输入输出面板 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入面板 */}
            <Card className="h-96">
              <IOPanel
                title="文本输入"
                placeholder="请输入要计算哈希的文本..."
                value={input}
                onChange={handleInputChange}
                actions={inputActions}
                maxLength={1048576}
                error={error}
              />
            </Card>

            {/* 输出面板 */}
            <Card className="h-96">
              <IOPanel
                title={`${hashType}哈希值`}
                placeholder={`${hashType}哈希值将显示在这里...`}
                value={isComputing ? '计算中...' : output}
                readonly
                actions={outputActions}
              />
            </Card>
          </div>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInput('Hello World')}
                >
                  Hello World
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInput('123456')}
                >
                  123456
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInput('admin')}
                >
                  admin
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInput('password')}
                >
                  password
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 算法对比 */}
          <Card>
            <CardHeader>
              <CardTitle>算法对比</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">算法</th>
                      <th className="text-left py-2">输出长度</th>
                      <th className="text-left py-2">安全性</th>
                      <th className="text-left py-2">推荐用途</th>
                    </tr>
                  </thead>
                  <tbody className="text-gray-600">
                    <tr className="border-b">
                      <td className="py-2 font-medium">SHA-1</td>
                      <td className="py-2">160位 (40字符)</td>
                      <td className="py-2 text-orange-600">已弃用</td>
                      <td className="py-2">仅用于兼容性</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-2 font-medium">SHA-256</td>
                      <td className="py-2">256位 (64字符)</td>
                      <td className="py-2 text-green-600">高</td>
                      <td className="py-2">通用安全应用</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-2 font-medium">SHA-384</td>
                      <td className="py-2">384位 (96字符)</td>
                      <td className="py-2 text-green-600">很高</td>
                      <td className="py-2">高安全要求</td>
                    </tr>
                    <tr>
                      <td className="py-2 font-medium">SHA-512</td>
                      <td className="py-2">512位 (128字符)</td>
                      <td className="py-2 text-green-600">最高</td>
                      <td className="py-2">最高安全要求</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>SHA哈希算法:</strong> 安全哈希算法(Secure Hash Algorithm)，用于生成数据的数字指纹</p>
                <p><strong>主要用途:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>数据完整性验证</li>
                  <li>密码安全存储</li>
                  <li>数字签名和证书</li>
                  <li>区块链和加密货币</li>
                  <li>文件校验和验证</li>
                </ul>
                <p><strong>安全建议:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>避免使用SHA-1，已被证明存在安全漏洞</li>
                  <li>SHA-256是目前最常用的安全选择</li>
                  <li>对于高安全要求，推荐使用SHA-384或SHA-512</li>
                  <li>相同输入总是产生相同输出</li>
                  <li>哈希过程不可逆，无法从哈希值还原原文</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ShaHashPage;
