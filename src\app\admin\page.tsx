'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Wrench, 
  Users, 
  BarChart3,
  CheckCircle,
  XCircle,
  TrendingUp
} from 'lucide-react';

interface SystemStats {
  database: {
    connected: boolean;
    tools: number;
    users: number;
  };
  tools: {
    total: number;
    byCategory: Array<{ category: string; count: number }>;
    needSync: boolean;
  };
  system: {
    uptime: string;
    version: string;
    environment: string;
  };
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);

  const getSystemStats = async () => {
    setLoading(true);
    try {
      // 获取数据库状态
      const dbResponse = await fetch('/api/db/test');
      const dbResult = await dbResponse.json();

      // 获取工具统计
      const toolsResponse = await fetch('/api/tools/sync?action=count');
      const toolsResult = await toolsResponse.json();

      // 获取数据库统计
      const statsResponse = await fetch('/api/db/manage?action=stats');
      const statsResult = await statsResponse.json();

      setStats({
        database: {
          connected: dbResult.success,
          tools: toolsResult.success ? toolsResult.data.database : 0,
          users: statsResult.success ? statsResult.data.totalUsers : 0,
        },
        tools: {
          total: toolsResult.success ? toolsResult.data.constants : 0,
          byCategory: statsResult.success ? statsResult.data.toolsByCategory : [],
          needSync: toolsResult.success ? toolsResult.data.needSync : false,
        },
        system: {
          uptime: '运行中',
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
        },
      });
    } catch (error) {
      console.error('获取系统统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getSystemStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">管理后台概览</h1>
        <p className="text-gray-600">Tool List 系统管理和监控</p>
      </div>

      {/* 系统状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 数据库状态 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库状态</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {stats?.database.connected ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              <span className="text-2xl font-bold">
                {stats?.database.connected ? '已连接' : '未连接'}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              MongoDB 连接状态
            </p>
          </CardContent>
        </Card>

        {/* 工具数量 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">工具总数</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.tools.total || 0}</div>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-xs text-muted-foreground">
                数据库: {stats?.database.tools || 0}
              </p>
              {stats?.tools.needSync && (
                <Badge variant="destructive" className="text-xs">
                  需同步
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 用户数量 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户数量</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.database.users || 0}</div>
            <p className="text-xs text-muted-foreground mt-1">
              注册用户总数
            </p>
          </CardContent>
        </Card>

        {/* 系统状态 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-2xl font-bold">正常</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats?.system.environment} 环境
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 工具分类统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="w-5 h-5" />
              工具分类统计
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stats?.tools.byCategory.length ? (
              <div className="space-y-3">
                {stats.tools.byCategory.map((category) => (
                  <div key={category.category} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category.category}</span>
                    <Badge variant="outline">{category.count}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                暂无分类统计数据
              </p>
            )}
          </CardContent>
        </Card>

        {/* 系统信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              系统信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">版本</span>
                <Badge variant="outline">{stats?.system.version}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">环境</span>
                <Badge variant="outline">{stats?.system.environment}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">运行状态</span>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">{stats?.system.uptime}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/admin/database"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <Database className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-medium">数据库管理</h3>
                <p className="text-sm text-gray-600">管理数据库连接和数据</p>
              </div>
            </a>

            <a
              href="/admin/tools"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <Wrench className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-medium">工具管理</h3>
                <p className="text-sm text-gray-600">同步和管理工具数据</p>
              </div>
            </a>

            <Link
              href="/"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <TrendingUp className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-medium">返回网站</h3>
                <p className="text-sm text-gray-600">查看前台网站</p>
              </div>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
