// 结构化数据生成器 - 用于SEO优化
export interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: unknown;
}

// 网站主体结构化数据
export const websiteStructuredData: StructuredData = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'Tool List',
  alternateName: '开发者工具集合',
  url: 'https://cypress.fun',
  description: '专业的在线开发工具集合平台，提供时间戳转换、JSON格式化、Base64编码等11个实用工具',
  inLanguage: 'zh-CN',
  potentialAction: {
    '@type': 'SearchAction',
    target: {
      '@type': 'EntryPoint',
      urlTemplate: 'https://cypress.fun/search?q={search_term_string}'
    },
    'query-input': 'required name=search_term_string'
  },
  publisher: {
    '@type': 'Organization',
    name: 'Tool List Team',
    url: 'https://cypress.fun',
    logo: {
      '@type': 'ImageObject',
      url: 'https://cypress.fun/logo.png',
      width: 512,
      height: 512
    }
  }
};

// 软件应用结构化数据
export const softwareApplicationStructuredData: StructuredData = {
  '@context': 'https://schema.org',
  '@type': 'SoftwareApplication',
  name: 'Tool List - 开发者工具集合',
  applicationCategory: 'DeveloperApplication',
  operatingSystem: 'Web Browser',
  url: 'https://cypress.fun',
  description: '专业的在线开发工具集合，包含时间戳转换、JSON格式化、Base64编码、颜色转换、哈希计算等11个实用工具',
  softwareVersion: '1.0.0',
  datePublished: '2024-12-19',
  dateModified: new Date().toISOString().split('T')[0],
  author: {
    '@type': 'Organization',
    name: 'Tool List Team',
    url: 'https://github.com/butterfly4147/toollist'
  },
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'CNY',
    availability: 'https://schema.org/InStock'
  },
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '4.8',
    ratingCount: '150',
    bestRating: '5',
    worstRating: '1'
  },
  featureList: [
    'Unix时间戳转换器',
    'JSON格式化工具',
    'Base64编码解码',
    '颜色格式转换',
    '大小写转换',
    'URL编码解码',
    'IP地址转换',
    'QR码生成器',
    'SHA哈希计算',
    '图片压缩工具',
    '文本转换工具'
  ]
};

// 工具页面结构化数据生成器
export const generateToolStructuredData = (tool: {
  id: string;
  name: string;
  description: string;
  category: string;
  url: string;
}): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'WebApplication',
  name: tool.name,
  description: tool.description,
  url: tool.url,
  applicationCategory: 'DeveloperApplication',
  operatingSystem: 'Web Browser',
  browserRequirements: 'Requires JavaScript. Requires HTML5.',
  softwareVersion: '1.0.0',
  author: {
    '@type': 'Organization',
    name: 'Tool List Team'
  },
  isPartOf: {
    '@type': 'WebSite',
    name: 'Tool List',
    url: 'https://cypress.fun'
  },
  mainEntity: {
    '@type': 'Thing',
    name: tool.name,
    description: tool.description,
    category: tool.category
  }
});

// 面包屑导航结构化数据生成器
export const generateBreadcrumbStructuredData = (breadcrumbs: Array<{
  name: string;
  url: string;
}>): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: breadcrumbs.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url
  }))
});

// FAQ结构化数据生成器
export const generateFAQStructuredData = (faqs: Array<{
  question: string;
  answer: string;
}>): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map(faq => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer
    }
  }))
});

// 文章结构化数据生成器
export const generateArticleStructuredData = (article: {
  title: string;
  description: string;
  url: string;
  datePublished: string;
  dateModified: string;
  author: string;
  image?: string;
}): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'Article',
  headline: article.title,
  description: article.description,
  url: article.url,
  datePublished: article.datePublished,
  dateModified: article.dateModified,
  author: {
    '@type': 'Person',
    name: article.author
  },
  publisher: {
    '@type': 'Organization',
    name: 'Tool List',
    logo: {
      '@type': 'ImageObject',
      url: 'https://cypress.fun/logo.png'
    }
  },
  ...(article.image && {
    image: {
      '@type': 'ImageObject',
      url: article.image,
      width: 1200,
      height: 630
    }
  })
});

// 组织信息结构化数据
export const organizationStructuredData: StructuredData = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Tool List',
  alternateName: '开发者工具集合',
  url: 'https://cypress.fun',
  logo: 'https://cypress.fun/logo.png',
  description: '专业的在线开发工具集合平台',
  foundingDate: '2024-12-19',
  contactPoint: {
    '@type': 'ContactPoint',
    email: '<EMAIL>',
    contactType: 'customer service',
    availableLanguage: ['Chinese', 'English']
  },
  sameAs: [
    'https://github.com/butterfly4147/toollist'
  ]
};

// 生成结构化数据JSON-LD脚本标签
export const generateStructuredDataScript = (data: StructuredData | StructuredData[]) => {
  const jsonData = Array.isArray(data) ? data : [data];
  return {
    __html: JSON.stringify(jsonData, null, 2)
  };
};

// 常用工具的结构化数据
export const toolsStructuredData = {
  timestamp: generateToolStructuredData({
    id: 'timestamp',
    name: 'Unix时间戳转换器',
    description: '在线Unix时间戳转换工具，支持时间戳与日期格式互转，多时区支持',
    category: '时间工具',
    url: 'https://cypress.fun/tools/timestamp'
  }),

  json: generateToolStructuredData({
    id: 'json-formatter',
    name: 'JSON格式化工具',
    description: '在线JSON格式化、验证、压缩工具，支持语法高亮和错误检查',
    category: '文本处理',
    url: 'https://cypress.fun/tools/json-formatter'
  }),

  base64: generateToolStructuredData({
    id: 'base64-converter',
    name: 'Base64编码解码',
    description: '在线Base64编码解码工具，支持文本和文件的Base64转换',
    category: '编码转换',
    url: 'https://cypress.fun/tools/base64-converter'
  }),

  color: generateToolStructuredData({
    id: 'color-converter',
    name: '颜色格式转换',
    description: '在线颜色格式转换工具，支持HEX、RGB、HSL、HSV、CMYK格式互转',
    category: '设计工具',
    url: 'https://cypress.fun/tools/color-converter'
  }),

  hash: generateToolStructuredData({
    id: 'sha-hash',
    name: 'SHA哈希计算',
    description: '在线SHA哈希计算工具，支持SHA-1、SHA-256、SHA-384、SHA-512',
    category: '安全工具',
    url: 'https://cypress.fun/tools/sha-hash'
  })
};
