'use client';

import React from 'react';
import { useToolsStore } from '@/store';
import { TOOLS } from '@/lib/constants/tools';
import ToolCard from '@/components/tools/ToolCard';
import { Heart } from 'lucide-react';

export default function FavoritesPage() {
  const { favoriteTools: favoriteIds } = useToolsStore();

  const favoriteTools = TOOLS.filter(tool => favoriteIds?.includes(tool.id) || false);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Heart className="w-8 h-8 text-red-500 fill-current" />
            <h1 className="text-3xl font-bold text-gray-900">我的收藏</h1>
          </div>
          <p className="text-gray-600">
            您收藏的工具将显示在这里，方便快速访问
          </p>
        </div>

        {favoriteTools.length === 0 ? (
          <div className="text-center py-16">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-500 mb-2">
              还没有收藏任何工具
            </h3>
            <p className="text-gray-400 mb-6">
              浏览工具页面，点击心形图标来收藏您喜欢的工具
            </p>
            <a
              href="/tools"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              浏览所有工具
            </a>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favoriteTools.map((tool) => (
              <ToolCard key={tool.id} tool={tool} />
            ))}
          </div>
        )}

        {favoriteTools.length > 0 && (
          <div className="text-center mt-12">
            <p className="text-gray-500 mb-4">
              共收藏了 {favoriteTools.length} 个工具
            </p>
            <a
              href="/tools"
              className="inline-flex items-center px-4 py-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              发现更多工具 →
            </a>
          </div>
        )}
      </div>
    </div>
  );
}
