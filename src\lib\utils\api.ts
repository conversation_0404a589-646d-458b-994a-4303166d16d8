import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

// API 错误类
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 成功响应
export function successResponse<T>(
  data: T,
  message?: string,
  statusCode = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
    },
    { status: statusCode }
  );
}

// 错误响应
export function errorResponse(
  error: string | Error | ApiError,
  statusCode = 500
): NextResponse<ApiResponse> {
  let message: string;
  let code: number;

  if (error instanceof ApiError) {
    message = error.message;
    code = error.statusCode;
  } else if (error instanceof Error) {
    message = error.message;
    code = statusCode;
  } else {
    message = error;
    code = statusCode;
  }

  console.error('API Error:', { message, code, stack: error instanceof Error ? error.stack : undefined });

  return NextResponse.json(
    {
      success: false,
      error: message,
    },
    { status: code }
  );
}

// 分页响应
export function paginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
) {
  const totalPages = Math.ceil(total / limit);

  return NextResponse.json({
    success: true,
    data,
    message,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  });
}

// 验证用户身份（简化版本）
export async function requireAuth() {
  // 简化的认证检查，实际项目中需要完整的实现
  // 这里返回模拟的用户数据
  return {
    id: '1',
    email: '<EMAIL>',
    name: 'User',
    role: 'user'
  };
}

// 验证管理员权限
export async function requireAdmin() {
  const user = await requireAuth();

  if (user.role !== 'admin') {
    throw new ApiError(403, '需要管理员权限');
  }

  return user;
}

// 验证请求方法
export function validateMethod(request: NextRequest, allowedMethods: string[]) {
  if (!allowedMethods.includes(request.method)) {
    throw new ApiError(405, `方法 ${request.method} 不被允许`);
  }
}

// 验证请求体
export async function validateBody<T>(
  request: NextRequest,
  validator: (data: unknown) => T
): Promise<T> {
  try {
    const body = await request.json();
    return validator(body);
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new ApiError(400, '无效的 JSON 格式');
    }
    throw error;
  }
}

// 获取查询参数
export function getQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const params: Record<string, string> = {};

  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
}

// 获取分页参数
export function getPaginationParams(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20', 10)));
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

// API 路由包装器（简化版本）
export function withApiHandler(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return async (request: NextRequest, context?: any) => {
    try {
      // 执行处理器
      return await handler(request, context);
    } catch (error) {
      // 统一错误处理
      if (error instanceof ApiError) {
        return errorResponse(error);
      }

      // 未知错误
      console.error('Unhandled API error:', error);
      return errorResponse('服务器内部错误', 500);
    }
  };
}

// 速率限制（简单实现）
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(
  identifier: string,
  maxRequests = 100,
  windowMs = 15 * 60 * 1000 // 15分钟
): boolean {
  const now = Date.now();

  // 清理过期记录
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < now) {
      rateLimitMap.delete(key);
    }
  }

  const current = rateLimitMap.get(identifier);

  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.resetTime < now) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.count >= maxRequests) {
    return false;
  }

  current.count++;
  return true;
}

// 获取客户端 IP
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

// 记录 API 使用情况
export async function logApiUsage(
  request: NextRequest,
  endpoint: string,
  userId?: string,
  duration?: number
) {
  try {
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 这里可以记录到数据库或日志服务
    console.log('API Usage:', {
      endpoint,
      method: request.method,
      userId,
      ip,
      userAgent,
      duration,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to log API usage:', error);
  }
}
