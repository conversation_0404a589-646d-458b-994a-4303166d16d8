// Multi-language translations
const translations = {
    'en-US': {
        seoTitle: 'Unix Timestamp Converter - Free Online Tool for Developers',
        seoDescription: 'Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.',
        seoKeywords: 'Unix timestamp, timestamp converter, epoch converter, date converter, developer tools',
        mainTitle: 'Unix Timestamp Converter',
        featuresLink: '✨ Features',
        caseConverterLink: '🔤 Case Converter',
        tipText: '💡 Tip: Double-click copy buttons for quick copy',
        tipTextMobile: '💡 Tip: Long press copy buttons for quick copy',
        currentTimestampS: 'Current Timestamp (s)',
        currentTimestampMs: 'Current Timestamp (ms)',
        currentTime: 'Current Time',
        timestampConversionTitle: 'Timestamp Conversion',
        timestampConversionDesc: 'Supports seconds, milliseconds, microseconds and nanoseconds',
        dateConversionTitle: 'Date Conversion',
        convertBtn: 'Convert',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Local Timezone',
        relativeTimeLabel: 'Relative Time',
        timestampSLabel: 'Timestamp (s)',
        timestampMsLabel: 'Timestamp (ms)',
        minLater: 'min later',
        copyBtn: 'Copy',
        quickCopy1min: '1 min later',
        quickCopy3min: '3 min later',
        quickCopy5min: '5 min later',
        customMinutesPlaceholder: '5',
        aboutTitle: 'About Unix Timestamp',
        whatIsTitle: 'What is Unix Timestamp?',
        whatIsDesc: 'Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).',
        timeRangeTitle: 'Time Range',
        startTime: '- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0',
        endTime: '- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647',
        note64bit: '* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.',
        commonUnitsTitle: 'Common Units',
        secondsDesc: 'Seconds: Most commonly used, 10 digits',
        millisecondsDesc: 'Milliseconds: 1/1000 of a second, 13 digits',
        microsecondsDesc: 'Microseconds: 1/1,000,000 of a second, 16 digits',
        nanosecondsDesc: 'Nanoseconds: 1/1,000,000,000 of a second, 19 digits',
        whyUseTitle: 'Why Use Timestamps?',
        unifiedStandard: 'Unified standard: Not affected by time zones',
        easyCalculation: 'Easy calculation: Can be directly compared',
        storageEfficient: 'Storage efficient: Represents complete date and time with a single number',
        crossPlatform: 'Cross-platform: Supported by all mainstream programming languages',
        year2038Title: 'Year 2038 Problem',
        year2038Desc: 'On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.',
        footerText: '© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.'
    },
    'zh-CN': {
        seoTitle: 'Unix 时间戳转换器 - 免费在线工具',
        seoDescription: '免费在线Unix时间戳转换工具，支持秒、毫秒、微秒和纳秒的转换，适合开发者和系统管理员。',
        seoKeywords: 'Unix时间戳, 时间戳转换, epoch转换, 日期转换, 开发者工具',
        mainTitle: 'Unix 时间戳转换器',
        featuresLink: '✨ 功能特性',
        caseConverterLink: '🔤 大小写转换',
        tipText: '💡 提示：双击复制按钮快速复制',
        tipTextMobile: '💡 提示：长按复制按钮快速复制',
        currentTimestampS: '当前时间戳（秒）',
        currentTimestampMs: '当前时间戳（毫秒）',
        currentTime: '当前时间',
        timestampConversionTitle: '时间戳转换',
        timestampConversionDesc: '支持秒、毫秒、微秒和纳秒',
        dateConversionTitle: '日期转换',
        convertBtn: '转换',
        formatLabel: '格式',
        gmtLabel: 'GMT时间',
        localTimezoneLabel: '本地时区',
        relativeTimeLabel: '相对时间',
        timestampSLabel: '时间戳（秒）',
        timestampMsLabel: '时间戳（毫秒）',
        minLater: '分钟后',
        copyBtn: '复制',
        quickCopy1min: '1分钟后',
        quickCopy3min: '3分钟后',
        quickCopy5min: '5分钟后',
        customMinutesPlaceholder: '5',
        aboutTitle: '关于 Unix 时间戳',
        whatIsTitle: '什么是 Unix 时间戳？',
        whatIsDesc: 'Unix 时间戳是一个整数，表示自1970年1月1日 00:00:00 UTC（Unix纪元）以来经过的秒数。',
        timeRangeTitle: '时间范围',
        startTime: '- 开始时间：1970年1月1日 00:00:00 UTC，时间戳：0',
        endTime: '- 结束时间：2038年1月19日 03:14:07 UTC，时间戳：2,147,483,647',
        note64bit: '* 注意：此限制基于32位系统。64位系统可以表示±292,277,026,596年。',
        commonUnitsTitle: '常用单位',
        secondsDesc: '秒：最常用，10位数字',
        millisecondsDesc: '毫秒：1/1000秒，13位数字',
        microsecondsDesc: '微秒：1/1,000,000秒，16位数字',
        nanosecondsDesc: '纳秒：1/1,000,000,000秒，19位数字',
        whyUseTitle: '为什么使用时间戳？',
        unifiedStandard: '统一标准：不受时区影响',
        easyCalculation: '易于计算：可以直接比较',
        storageEfficient: '存储高效：用单个数字表示完整的日期和时间',
        crossPlatform: '跨平台：所有主流编程语言都支持',
        year2038Title: '2038年问题',
        year2038Desc: '在32位系统上，Unix时间戳将在2038年1月19日 03:14:07 UTC达到最大值2,147,483,647，可能导致溢出问题。现代64位系统不受此限制影响。',
        footerText: '© 2025 Unix 时间戳转换器 - 为开发者和系统管理员提供的实用工具。'
    },
    'ja-JP': {
        seoTitle: 'Unix タイムスタンプ変換器 - 無料オンラインツール',
        seoDescription: '無料のオンラインUnixタイムスタンプ変換ツール。秒、ミリ秒、マイクロ秒、ナノ秒の変換をサポート。',
        seoKeywords: 'Unixタイムスタンプ, タイムスタンプ変換, epoch変換, 日付変換, 開発者ツール',
        mainTitle: 'Unix タイムスタンプ変換器',
        featuresLink: '✨ 機能',
        caseConverterLink: '🔤 大文字小文字変換',
        tipText: '💡 ヒント：コピーボタンをダブルクリックで高速コピー',
        tipTextMobile: '💡 ヒント：コピーボタンを長押しで高速コピー',
        currentTimestampS: '現在のタイムスタンプ（秒）',
        currentTimestampMs: '現在のタイムスタンプ（ミリ秒）',
        currentTime: '現在時刻',
        timestampConversionTitle: 'タイムスタンプ変換',
        timestampConversionDesc: '秒、ミリ秒、マイクロ秒、ナノ秒をサポート',
        dateConversionTitle: '日付変換',
        convertBtn: '変換',
        formatLabel: 'フォーマット',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'ローカルタイムゾーン',
        relativeTimeLabel: '相対時間',
        timestampSLabel: 'タイムスタンプ（秒）',
        timestampMsLabel: 'タイムスタンプ（ミリ秒）',
        minLater: '分後',
        copyBtn: 'コピー',
        quickCopy1min: '1分後',
        quickCopy3min: '3分後',
        quickCopy5min: '5分後',
        customMinutesPlaceholder: '5'
    }
    // Add more languages as needed
};

// Global variables
let currentLang = 'en-US';
let currentTimestamp = 0;
let updateTimer = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
    initializeEventListeners();
    updateCurrentTime();
    startTimeUpdater();
    detectMobile();
});

// Language detection and initialization
function initializeLanguage() {
    // Detect language from URL path
    const path = window.location.pathname;
    const langMatch = path.match(/\/([a-z]{2}-[A-Z]{2})\//);

    if (langMatch && translations[langMatch[1]]) {
        currentLang = langMatch[1];
    } else {
        // Fallback to browser language
        const browserLang = navigator.language || navigator.userLanguage;
        if (translations[browserLang]) {
            currentLang = browserLang;
        }
    }

    updateUIText();
    updateSEOTags();
}

// Update UI text based on current language
function updateUIText() {
    const lang = translations[currentLang];
    if (!lang) return;

    // Update all text elements
    document.getElementById('main-title').textContent = lang.mainTitle;
    document.getElementById('features-link').textContent = lang.featuresLink;
    document.getElementById('case-converter-link').textContent = lang.caseConverterLink;
    document.getElementById('current-language').textContent = getLanguageDisplayName(currentLang);

    // Update labels
    document.getElementById('current-timestamp-s-label').textContent = lang.currentTimestampS;
    document.getElementById('current-timestamp-ms-label').textContent = lang.currentTimestampMs;
    document.getElementById('current-time-label').textContent = lang.currentTime;
    document.getElementById('timestamp-conversion-title').textContent = lang.timestampConversionTitle;
    document.getElementById('timestamp-conversion-desc').textContent = lang.timestampConversionDesc;
    document.getElementById('date-conversion-title').textContent = lang.dateConversionTitle;

    // Update buttons
    document.getElementById('timestamp-convert-btn').textContent = lang.convertBtn;
    document.getElementById('date-convert-btn').textContent = lang.convertBtn;

    // Update result labels
    document.getElementById('format-label').textContent = lang.formatLabel;
    document.getElementById('gmt-label').textContent = lang.gmtLabel;
    document.getElementById('local-timezone-label').textContent = lang.localTimezoneLabel;
    document.getElementById('relative-time-label').textContent = lang.relativeTimeLabel;
    document.getElementById('timestamp-s-label').textContent = lang.timestampSLabel;
    document.getElementById('timestamp-ms-label').textContent = lang.timestampMsLabel;
    document.getElementById('date-gmt-label').textContent = lang.gmtLabel;
    document.getElementById('date-local-label').textContent = lang.localTimezoneLabel;
    document.getElementById('date-relative-label').textContent = lang.relativeTimeLabel;

    // Update quick copy buttons
    updateQuickCopyButtons(lang);

    // Update about section
    updateAboutSection(lang);
}

// Update quick copy buttons text
function updateQuickCopyButtons(lang) {
    const quickCopyButtons = {
        'quick-1min-s': lang.quickCopy1min,
        'quick-3min-s': lang.quickCopy3min,
        'quick-5min-s': lang.quickCopy5min,
        'quick-1min-ms': lang.quickCopy1min,
        'quick-3min-ms': lang.quickCopy3min,
        'quick-5min-ms': lang.quickCopy5min,
        'quick-1min-time': lang.quickCopy1min,
        'quick-3min-time': lang.quickCopy3min,
        'quick-5min-time': lang.quickCopy5min
    };

    Object.entries(quickCopyButtons).forEach(([id, text]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = text;
    });

    // Update custom minutes labels
    const customLabels = ['custom-minutes-label-s', 'custom-minutes-label-ms', 'custom-minutes-label-time'];
    customLabels.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.textContent = lang.minLater;
    });

    // Update custom copy buttons
    const customCopyButtons = ['custom-copy-s', 'custom-copy-ms', 'custom-copy-time'];
    customCopyButtons.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.textContent = lang.copyBtn;
    });
}

// Update about section
function updateAboutSection(lang) {
    const aboutElements = {
        'about-title': lang.aboutTitle,
        'what-is-title': lang.whatIsTitle,
        'what-is-desc': lang.whatIsDesc,
        'time-range-title': lang.timeRangeTitle,
        'start-time': lang.startTime,
        'end-time': lang.endTime,
        'note-64bit': lang.note64bit,
        'common-units-title': lang.commonUnitsTitle,
        'seconds-desc': lang.secondsDesc,
        'milliseconds-desc': lang.millisecondsDesc,
        'microseconds-desc': lang.microsecondsDesc,
        'nanoseconds-desc': lang.nanosecondsDesc,
        'why-use-title': lang.whyUseTitle,
        'unified-standard': lang.unifiedStandard,
        'easy-calculation': lang.easyCalculation,
        'storage-efficient': lang.storageEfficient,
        'cross-platform': lang.crossPlatform,
        'year-2038-title': lang.year2038Title,
        'year-2038-desc': lang.year2038Desc,
        'footer-text': lang.footerText
    };

    Object.entries(aboutElements).forEach(([id, text]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = text;
    });
}

// Update SEO tags
function updateSEOTags() {
    const lang = translations[currentLang];
    if (!lang) return;

    document.title = lang.seoTitle;
    document.querySelector('meta[name="description"]').setAttribute('content', lang.seoDescription);
    document.querySelector('meta[name="keywords"]').setAttribute('content', lang.seoKeywords);
}

// Get language display name
function getLanguageDisplayName(langCode) {
    const displayNames = {
        'en-US': 'English (US)',
        'zh-CN': '中文 (中国)',
        'hi-IN': 'हिन्दी (भारत)',
        'ja-JP': '日本語 (日本)',
        'de-DE': 'Deutsch (Deutschland)',
        'en-GB': 'English (UK)',
        'ru-RU': 'Русский (Россия)',
        'ko-KR': '한국어 (대한민국)',
        'en-CA': 'English (Canada)',
        'fr-FR': 'Français (France)'
    };
    return displayNames[langCode] || langCode;
}

// Initialize event listeners
function initializeEventListeners() {
    // Language selector
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');

    languageBtn.addEventListener('click', function() {
        languageDropdown.classList.toggle('show');
    });

    // Language options
    document.querySelectorAll('.language-option').forEach(option => {
        option.addEventListener('click', function() {
            const newLang = this.getAttribute('data-lang');
            changeLanguage(newLang);
            languageDropdown.classList.remove('show');
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!languageBtn.contains(event.target) && !languageDropdown.contains(event.target)) {
            languageDropdown.classList.remove('show');
        }
    });

    // Copy buttons with double-click support
    initializeCopyButtons();

    // Conversion buttons
    document.getElementById('timestamp-convert-btn').addEventListener('click', convertTimestamp);
    document.getElementById('date-convert-btn').addEventListener('click', convertDate);

    // Real-time conversion on input
    document.getElementById('timestamp-input').addEventListener('input', convertTimestamp);
    document.getElementById('date-input').addEventListener('input', convertDate);
}

// Initialize copy buttons
function initializeCopyButtons() {
    const copyButtons = document.querySelectorAll('.copy-btn');

    copyButtons.forEach(button => {
        let clickCount = 0;
        let clickTimer = null;

        button.addEventListener('click', function(e) {
            e.preventDefault();
            clickCount++;

            if (clickCount === 1) {
                clickTimer = setTimeout(() => {
                    // Single click - normal copy
                    const value = this.getAttribute('data-value') || this.previousElementSibling.textContent;
                    copyToClipboard(value);
                    clickCount = 0;
                }, 300);
            } else if (clickCount === 2) {
                // Double click - show quick copy menu
                clearTimeout(clickTimer);
                showQuickCopyMenu(this);
                clickCount = 0;
            }
        });
    });
}

// Show quick copy menu
function showQuickCopyMenu(button) {
    // Hide all other menus first
    document.querySelectorAll('.quick-copy-menu').forEach(menu => {
        menu.classList.remove('show');
    });

    // Show the menu for this button
    const menu = button.nextElementSibling;
    if (menu && menu.classList.contains('quick-copy-menu')) {
        menu.classList.add('show');

        // Initialize quick copy buttons in this menu
        initializeQuickCopyButtons(menu, button);
    }
}

// Initialize quick copy buttons in menu
function initializeQuickCopyButtons(menu, originalButton) {
    const quickButtons = menu.querySelectorAll('.quick-copy-btn');
    const customInput = menu.querySelector('input[type="number"]');
    const customButton = menu.querySelector('.custom-copy-btn');

    quickButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const minutes = parseInt(this.getAttribute('data-minutes'));
            const futureTimestamp = Math.floor(Date.now() / 1000) + (minutes * 60);

            let value;
            if (originalButton.id.includes('ms')) {
                value = (futureTimestamp * 1000).toString();
            } else if (originalButton.id.includes('time')) {
                value = new Date(futureTimestamp * 1000).toLocaleString();
            } else {
                value = futureTimestamp.toString();
            }

            copyToClipboard(value);
            menu.classList.remove('show');
        });
    });

    if (customButton && customInput) {
        customButton.addEventListener('click', function() {
            const minutes = parseInt(customInput.value) || 5;
            const futureTimestamp = Math.floor(Date.now() / 1000) + (minutes * 60);

            let value;
            if (originalButton.id.includes('ms')) {
                value = (futureTimestamp * 1000).toString();
            } else if (originalButton.id.includes('time')) {
                value = new Date(futureTimestamp * 1000).toLocaleString();
            } else {
                value = futureTimestamp.toString();
            }

            copyToClipboard(value);
            menu.classList.remove('show');

            // Save user preference
            localStorage.setItem('customMinutes', minutes.toString());
        });

        // Load saved preference
        const savedMinutes = localStorage.getItem('customMinutes');
        if (savedMinutes) {
            customInput.value = savedMinutes;
        }
    }
}

// Change language
function changeLanguage(newLang) {
    if (translations[newLang]) {
        currentLang = newLang;
        updateUIText();
        updateSEOTags();

        // Update URL if needed (for SEO)
        const newPath = newLang === 'en-US' ? '/' : `/${newLang}/`;
        if (window.location.pathname !== newPath) {
            window.history.pushState({}, '', newPath);
        }
    }
}

// Update current time display
function updateCurrentTime() {
    const now = new Date();
    currentTimestamp = Math.floor(now.getTime() / 1000);

    // Update displays
    document.getElementById('current-timestamp-s').textContent = currentTimestamp;
    document.getElementById('current-timestamp-ms').textContent = now.getTime();
    document.getElementById('current-time').textContent = now.toLocaleString();

    // Update copy button data attributes
    document.getElementById('copy-current-s').setAttribute('data-value', currentTimestamp);
    document.getElementById('copy-current-ms').setAttribute('data-value', now.getTime());
    document.getElementById('copy-current-time').setAttribute('data-value', now.toLocaleString());
}

// Start time updater
function startTimeUpdater() {
    updateTimer = setInterval(updateCurrentTime, 1000);
}

// Detect mobile device
function detectMobile() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const tipElement = document.getElementById('tip-text');

    if (isMobile && tipElement) {
        const lang = translations[currentLang];
        tipElement.textContent = lang.tipTextMobile || lang.tipText;
    }
}

// Convert timestamp
function convertTimestamp() {
    const input = document.getElementById('timestamp-input').value.trim();
    const resultsDiv = document.getElementById('timestamp-results');

    if (!input) {
        resultsDiv.style.display = 'none';
        return;
    }

    const timestamp = parseFloat(input);
    if (isNaN(timestamp)) {
        resultsDiv.style.display = 'none';
        return;
    }

    // Detect format based on length
    let date;
    let format;

    if (input.length <= 10) {
        // Seconds
        date = new Date(timestamp * 1000);
        format = 'Seconds (10 digits)';
    } else if (input.length <= 13) {
        // Milliseconds
        date = new Date(timestamp);
        format = 'Milliseconds (13 digits)';
    } else if (input.length <= 16) {
        // Microseconds
        date = new Date(timestamp / 1000);
        format = 'Microseconds (16 digits)';
    } else {
        // Nanoseconds
        date = new Date(timestamp / 1000000);
        format = 'Nanoseconds (19 digits)';
    }

    if (isNaN(date.getTime())) {
        resultsDiv.style.display = 'none';
        return;
    }

    // Update results
    document.getElementById('format-value').textContent = format;
    document.getElementById('gmt-value').textContent = date.toUTCString();
    document.getElementById('local-timezone-value').textContent = date.toLocaleString();
    document.getElementById('relative-time-value').textContent = getRelativeTime(date);

    resultsDiv.style.display = 'grid';
}

// Convert date
function convertDate() {
    const input = document.getElementById('date-input').value;
    const resultsDiv = document.getElementById('date-results');

    if (!input) {
        resultsDiv.style.display = 'none';
        return;
    }

    const date = new Date(input);
    if (isNaN(date.getTime())) {
        resultsDiv.style.display = 'none';
        return;
    }

    const timestampS = Math.floor(date.getTime() / 1000);
    const timestampMs = date.getTime();

    // Update results
    document.getElementById('timestamp-s-value').textContent = timestampS;
    document.getElementById('timestamp-ms-value').textContent = timestampMs;
    document.getElementById('date-gmt-value').textContent = date.toUTCString();
    document.getElementById('date-local-value').textContent = date.toLocaleString();
    document.getElementById('date-relative-value').textContent = getRelativeTime(date);

    resultsDiv.style.display = 'grid';
}

// Get relative time
function getRelativeTime(date) {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (Math.abs(diffYears) >= 1) {
        return diffYears > 0 ? `${diffYears} years ago` : `in ${Math.abs(diffYears)} years`;
    } else if (Math.abs(diffMonths) >= 1) {
        return diffMonths > 0 ? `${diffMonths} months ago` : `in ${Math.abs(diffMonths)} months`;
    } else if (Math.abs(diffDays) >= 1) {
        return diffDays > 0 ? `${diffDays} days ago` : `in ${Math.abs(diffDays)} days`;
    } else if (Math.abs(diffHours) >= 1) {
        return diffHours > 0 ? `${diffHours} hours ago` : `in ${Math.abs(diffHours)} hours`;
    } else if (Math.abs(diffMinutes) >= 1) {
        return diffMinutes > 0 ? `${diffMinutes} minutes ago` : `in ${Math.abs(diffMinutes)} minutes`;
    } else {
        return diffSeconds > 0 ? `${diffSeconds} seconds ago` : `in ${Math.abs(diffSeconds)} seconds`;
    }
}

// Copy to clipboard with multiple fallback methods
async function copyToClipboard(text) {
    try {
        // Method 1: Modern Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            showCopySuccess();
            return;
        }

        // Method 2: execCommand fallback
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showCopySuccess();
            return;
        }

        // Method 3: Manual copy dialog (last resort)
        showManualCopyDialog(text);

    } catch (err) {
        console.error('Copy failed:', err);
        showManualCopyDialog(text);
    }
}

// Show copy success animation
function showCopySuccess() {
    const successElement = document.getElementById('copy-success');
    successElement.classList.add('show');

    setTimeout(() => {
        successElement.classList.remove('show');
    }, 1000);
}

// Show manual copy dialog for unsupported browsers
function showManualCopyDialog(text) {
    const message = `Please copy manually:\n\n${text}`;

    // Try to select the text for easier copying
    if (window.getSelection) {
        const selection = window.getSelection();
        selection.removeAllRanges();

        const range = document.createRange();
        const textNode = document.createTextNode(text);
        document.body.appendChild(textNode);
        range.selectNode(textNode);
        selection.addRange(range);

        setTimeout(() => {
            document.body.removeChild(textNode);
        }, 100);
    }

    alert(message);
}

// Close quick copy menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.copy-btn') && !event.target.closest('.quick-copy-menu')) {
        document.querySelectorAll('.quick-copy-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});

// Handle page visibility change to pause/resume timer
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        if (updateTimer) {
            clearInterval(updateTimer);
            updateTimer = null;
        }
    } else {
        if (!updateTimer) {
            updateCurrentTime();
            startTimeUpdater();
        }
    }
});

// Handle page unload
window.addEventListener('beforeunload', function() {
    if (updateTimer) {
        clearInterval(updateTimer);
    }
});
