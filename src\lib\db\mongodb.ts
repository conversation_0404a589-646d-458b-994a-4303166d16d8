import mongoose from 'mongoose';
import dns from 'dns';

// 使用公共 DNS 服务器，避免本地路由器无法解析 TXT 记录导致 ETIMEOUT
dns.setServers(['8.8.8.8', '1.1.1.1']);

// 使用MONGO_URI环境变量简化MongoDB连接配置
console.log('尝试从环境变量加载MONGO_URI...');

const MONGODB_URI = process.env.MONGODB_URI;

interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// 在全局对象上缓存连接，避免在开发模式下重复连接
declare global {
  // eslint-disable-next-line no-var
  var mongoose: MongooseCache | undefined;
}

let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectDB(): Promise<typeof mongoose> {
  if (!MONGODB_URI) {
    throw new Error('请在 .env.local 文件中定义 MONGODB_URI 环境变量');
  }

  if (cached!.conn) {
    return cached!.conn;
  }

  if (!cached!.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 10, // 维护最多10个socket连接
      serverSelectionTimeoutMS: 5000, // 5秒后超时
      socketTimeoutMS: 45000, // 45秒后关闭socket
      family: 4, // 使用IPv4，跳过IPv6
    };

    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      console.log('✅ MongoDB 连接成功');
      return mongoose;
    });
  }

  try {
    cached!.conn = await cached!.promise;
  } catch (e) {
    cached!.promise = null;
    console.error('❌ MongoDB 连接失败:', e);
    throw e;
  }

  return cached!.conn;
}

// 断开连接
export async function disconnectDB(): Promise<void> {
  if (cached?.conn) {
    await cached.conn.disconnect();
    cached.conn = null;
    cached.promise = null;
    console.log('🔌 MongoDB 连接已断开');
  }
}

// 检查连接状态
export function getConnectionStatus(): string {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
  };

  return states[mongoose.connection.readyState as keyof typeof states] || 'unknown';
}

// 监听连接事件
mongoose.connection.on('connected', () => {
  console.log('🔗 Mongoose 连接到 MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ Mongoose 连接错误:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('🔌 Mongoose 断开连接');
});

// 优雅关闭
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  console.log('🛑 应用终止，MongoDB 连接已关闭');
  process.exit(0);
});

export default connectDB;
