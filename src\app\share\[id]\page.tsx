'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useShareStore } from '@/store/shareStore';
import { ShareLink } from '@/types/share';
import { TOOLS } from '@/lib/constants/tools';

const SharePage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const shareId = params.id as string;

  const [shareData, setShareData] = useState<ShareLink | null>(null);
  const [password, setPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const { loadShare } = useShareStore();

  // 加载分享数据
  const loadShareData = async (pwd?: string) => {
    try {
      setIsLoading(true);
      setError('');

      const data = await loadShare(shareId, pwd);

      if (data) {
        setShareData(data);
        setShowPasswordInput(false);
      } else {
        setError('分享不存在或已过期');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载失败';

      if (errorMessage.includes('密码')) {
        setShowPasswordInput(true);
        setError('请输入访问密码');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 处理密码提交
  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password.trim()) {
      loadShareData(password);
    }
  };

  // 获取工具信息
  const getToolInfo = (toolId: string) => {
    return TOOLS.find(tool => tool.id === toolId);
  };

  // 跳转到工具页面
  const goToTool = () => {
    if (shareData) {
      const tool = getToolInfo(shareData.toolId);
      if (tool) {
        // 可以考虑将分享的状态作为URL参数传递
        router.push(tool.path);
      }
    }
  };

  useEffect(() => {
    if (shareId) {
      loadShareData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shareId]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (showPasswordInput) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">🔒 需要密码</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    访问密码
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="请输入密码"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoFocus
                  />
                </div>
                {error && (
                  <p className="text-sm text-red-600">{error}</p>
                )}
                <div className="flex space-x-3">
                  <Button type="submit" className="flex-1">
                    访问
                  </Button>
                  <Link href="/">
                    <Button variant="outline">
                      返回首页
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !shareData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">分享不存在</h1>
          <p className="text-gray-600 mb-6">
            {error || '该分享链接不存在或已过期'}
          </p>
          <div className="space-x-4">
            <Link href="/">
              <Button>返回首页</Button>
            </Link>
            <Link href="/tools">
              <Button variant="outline">浏览工具</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const tool = getToolInfo(shareData.toolId);
  const state = shareData.state;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {tool?.icon} {shareData.toolName}
          </h1>
          <p className="text-gray-600">
            分享于 {new Date(shareData.createdAt).toLocaleString('zh-CN')}
          </p>
        </div>

        {/* 工具信息 */}
        {tool && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span className="text-2xl">{tool.icon}</span>
                <span>{tool.name}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{tool.description}</p>
              <div className="flex flex-wrap gap-2">
                {tool.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 text-gray-600 text-sm rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 分享内容 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 输入 */}
          {state.input && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">输入内容</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 max-h-64 overflow-y-auto">
                    {state.input}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 输出 */}
          {state.output && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">输出结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 max-h-64 overflow-y-auto">
                    {state.output}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 工具选项 */}
        {Object.keys(state.options).length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="text-lg">工具设置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(state.options).map(([key, value]) => (
                  <div key={key} className="bg-gray-50 p-3 rounded">
                    <div className="text-sm font-medium text-gray-600">{key}</div>
                    <div className="text-sm text-gray-800">
                      {typeof value === 'boolean' ? (value ? '是' : '否') : String(value)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-4 justify-center">
          <Button onClick={goToTool} className="px-6 py-2">
            使用此工具
          </Button>
          <Link href="/tools">
            <Button variant="outline">
              浏览更多工具
            </Button>
          </Link>
          <Link href="/">
            <Button variant="outline">
              返回首页
            </Button>
          </Link>
        </div>

        {/* 分享统计 */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>此分享已被查看 {shareData.viewCount} 次</p>
          {shareData.expiresAt && (
            <p>
              有效期至 {new Date(shareData.expiresAt).toLocaleString('zh-CN')}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SharePage;
