<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unix Timestamp Converter - Free Online Tool for Developers</title>
    <meta name="description" content="Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.">
    <meta name="keywords" content="Unix timestamp, timestamp converter, epoch converter, date converter, developer tools">

    <!-- Multi-language hreflang tags -->
    <link rel="alternate" hreflang="en" href="https://unixtime.help/" />
    <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/" />
    <link rel="alternate" hreflang="hi-IN" href="https://unixtime.help/hi-IN/" />
    <link rel="alternate" hreflang="ja-JP" href="https://unixtime.help/ja-JP/" />
    <link rel="alternate" hreflang="de-DE" href="https://unixtime.help/de-DE/" />
    <link rel="alternate" hreflang="en-GB" href="https://unixtime.help/en-GB/" />
    <link rel="alternate" hreflang="ru-RU" href="https://unixtime.help/ru-RU/" />
    <link rel="alternate" hreflang="ko-KR" href="https://unixtime.help/ko-KR/" />
    <link rel="alternate" hreflang="en-CA" href="https://unixtime.help/en-CA/" />
    <link rel="alternate" hreflang="fr-FR" href="https://unixtime.help/fr-FR/" />

    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⏰</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header>
            <div class="header-content">
                <h1 id="main-title">Unix Timestamp Converter</h1>
                <div class="header-nav">
                    <a href="features.html" id="features-link">✨ Features</a>
                    <a href="case-converter.html" id="case-converter-link">🔤 Case Converter</a>
                </div>
            </div>
        </header>

        <!-- Language Selector -->
        <div class="language-selector">
            <button class="language-btn" id="language-btn">
                <span id="current-language">English (US)</span>
            </button>
            <div class="language-dropdown" id="language-dropdown">
                <div class="language-option" data-lang="en-US">English (US)</div>
                <div class="language-option" data-lang="zh-CN">中文 (中国)</div>
                <div class="language-option" data-lang="hi-IN">हिन्दी (भारत)</div>
                <div class="language-option" data-lang="ja-JP">日本語 (日本)</div>
                <div class="language-option" data-lang="de-DE">Deutsch (Deutschland)</div>
                <div class="language-option" data-lang="en-GB">English (UK)</div>
                <div class="language-option" data-lang="ru-RU">Русский (Россия)</div>
                <div class="language-option" data-lang="ko-KR">한국어 (대한민국)</div>
                <div class="language-option" data-lang="en-CA">English (Canada)</div>
                <div class="language-option" data-lang="fr-FR">Français (France)</div>
            </div>
        </div>

        <!-- Tip -->
        <div class="tip" id="tip">
            <span id="tip-text">💡 Tip: Double-click copy buttons for quick copy</span>
        </div>

        <!-- Current Timestamp Section -->
        <section class="current-timestamp">
            <div class="timestamp-row">
                <div class="timestamp-item">
                    <label id="current-timestamp-s-label">Current Timestamp (s)</label>
                    <div class="timestamp-value">
                        <span id="current-timestamp-s">0</span>
                        <button class="copy-btn" id="copy-current-s" data-value="">📋</button>
                        <div class="quick-copy-menu" id="quick-copy-s">
                            <button class="quick-copy-btn" data-minutes="1" id="quick-1min-s">1 min later</button>
                            <button class="quick-copy-btn" data-minutes="3" id="quick-3min-s">3 min later</button>
                            <button class="quick-copy-btn" data-minutes="5" id="quick-5min-s">5 min later</button>
                            <div class="custom-minutes">
                                <input type="number" id="custom-minutes-s" placeholder="5" min="1" max="999999">
                                <span id="custom-minutes-label-s">min later</span>
                                <button class="custom-copy-btn" id="custom-copy-s">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timestamp-item">
                    <label id="current-timestamp-ms-label">Current Timestamp (ms)</label>
                    <div class="timestamp-value">
                        <span id="current-timestamp-ms">0</span>
                        <button class="copy-btn" id="copy-current-ms" data-value="">📋</button>
                        <div class="quick-copy-menu" id="quick-copy-ms">
                            <button class="quick-copy-btn" data-minutes="1" id="quick-1min-ms">1 min later</button>
                            <button class="quick-copy-btn" data-minutes="3" id="quick-3min-ms">3 min later</button>
                            <button class="quick-copy-btn" data-minutes="5" id="quick-5min-ms">5 min later</button>
                            <div class="custom-minutes">
                                <input type="number" id="custom-minutes-ms" placeholder="5" min="1" max="999999">
                                <span id="custom-minutes-label-ms">min later</span>
                                <button class="custom-copy-btn" id="custom-copy-ms">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timestamp-item">
                    <label id="current-time-label">Current Time</label>
                    <div class="timestamp-value">
                        <span id="current-time">Loading...</span>
                        <button class="copy-btn" id="copy-current-time" data-value="">📋</button>
                        <div class="quick-copy-menu" id="quick-copy-time">
                            <button class="quick-copy-btn" data-minutes="1" id="quick-1min-time">1 min later</button>
                            <button class="quick-copy-btn" data-minutes="3" id="quick-3min-time">3 min later</button>
                            <button class="quick-copy-btn" data-minutes="5" id="quick-5min-time">5 min later</button>
                            <div class="custom-minutes">
                                <input type="number" id="custom-minutes-time" placeholder="5" min="1" max="999999">
                                <span id="custom-minutes-label-time">min later</span>
                                <button class="custom-copy-btn" id="custom-copy-time">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Timestamp Conversion Section -->
        <section class="conversion-section">
            <h2 id="timestamp-conversion-title">Timestamp Conversion</h2>
            <p id="timestamp-conversion-desc">Supports seconds, milliseconds, microseconds and nanoseconds</p>

            <div class="input-group">
                <input type="text" id="timestamp-input" placeholder="1640995200">
                <button class="convert-btn" id="timestamp-convert-btn">Convert</button>
            </div>

            <div class="result-grid" id="timestamp-results" style="display: none;">
                <div class="result-item">
                    <label id="format-label">Format</label>
                    <span id="format-value">-</span>
                </div>
                <div class="result-item">
                    <label id="gmt-label">GMT</label>
                    <span id="gmt-value">-</span>
                </div>
                <div class="result-item">
                    <label id="local-timezone-label">Local Timezone</label>
                    <span id="local-timezone-value">-</span>
                </div>
                <div class="result-item">
                    <label id="relative-time-label">Relative Time</label>
                    <span id="relative-time-value">-</span>
                </div>
            </div>
        </section>

        <!-- Date Conversion Section -->
        <section class="conversion-section">
            <h2 id="date-conversion-title">Date Conversion</h2>

            <div class="input-group">
                <input type="datetime-local" id="date-input">
                <button class="convert-btn" id="date-convert-btn">Convert</button>
            </div>

            <div class="result-grid" id="date-results" style="display: none;">
                <div class="result-item">
                    <label id="timestamp-s-label">Timestamp (s)</label>
                    <span id="timestamp-s-value">-</span>
                </div>
                <div class="result-item">
                    <label id="timestamp-ms-label">Timestamp (ms)</label>
                    <span id="timestamp-ms-value">-</span>
                </div>
                <div class="result-item">
                    <label id="date-gmt-label">GMT</label>
                    <span id="date-gmt-value">-</span>
                </div>
                <div class="result-item">
                    <label id="date-local-label">Local Timezone</label>
                    <span id="date-local-value">-</span>
                </div>
                <div class="result-item">
                    <label id="date-relative-label">Relative Time</label>
                    <span id="date-relative-value">-</span>
                </div>
            </div>
        </section>

        <!-- About Unix Timestamp Section -->
        <section class="about-section">
            <h2 id="about-title">About Unix Timestamp</h2>

            <div class="about-content">
                <div class="about-item">
                    <h3 id="what-is-title">What is Unix Timestamp?</h3>
                    <p id="what-is-desc">Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).</p>
                </div>

                <div class="about-item">
                    <h3 id="time-range-title">Time Range</h3>
                    <ul>
                        <li id="start-time">- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0</li>
                        <li id="end-time">- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647</li>
                        <li id="note-64bit">* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.</li>
                    </ul>
                </div>

                <div class="about-item">
                    <h3 id="common-units-title">Common Units</h3>
                    <ul>
                        <li id="seconds-desc">Seconds: Most commonly used, 10 digits</li>
                        <li id="milliseconds-desc">Milliseconds: 1/1000 of a second, 13 digits</li>
                        <li id="microseconds-desc">Microseconds: 1/1,000,000 of a second, 16 digits</li>
                        <li id="nanoseconds-desc">Nanoseconds: 1/1,000,000,000 of a second, 19 digits</li>
                    </ul>
                </div>

                <div class="about-item">
                    <h3 id="why-use-title">Why Use Timestamps?</h3>
                    <ul>
                        <li id="unified-standard">Unified standard: Not affected by time zones</li>
                        <li id="easy-calculation">Easy calculation: Can be directly compared</li>
                        <li id="storage-efficient">Storage efficient: Represents complete date and time with a single number</li>
                        <li id="cross-platform">Cross-platform: Supported by all mainstream programming languages</li>
                    </ul>
                </div>

                <div class="about-item">
                    <h3 id="year-2038-title">Year 2038 Problem</h3>
                    <p id="year-2038-desc">On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.</p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p id="footer-text">© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.</p>
        </footer>
    </div>

    <!-- Copy Success Notification -->
    <div class="copy-success" id="copy-success">
        <span>✓</span>
    </div>

    <script src="script.js"></script>
</body>
</html>
