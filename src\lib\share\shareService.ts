import {
  ShareableState,
  ShareLink,
  ShareOptions,
  ShareResult,
  ShareError,
  ShareErrorType,
  ParsedShareUrl,
  ShareValidation,
  DEFAULT_SHARE_CONFIG
} from '@/types/share';

// 生成短链接ID
export const generateShortId = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 生成分享ID
export const generateShareId = (): string => {
  return `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 验证分享状态
export const validateShareState = (state: ShareableState): ShareValidation => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需字段
  if (!state.toolId) {
    errors.push('工具ID不能为空');
  }

  if (!state.toolName) {
    errors.push('工具名称不能为空');
  }

  if (!state.input && !state.output) {
    warnings.push('输入和输出都为空，分享可能没有意义');
  }

  // 检查数据大小
  const stateJson = JSON.stringify(state);
  const estimatedSize = new Blob([stateJson]).size;

  if (estimatedSize > 1024 * 1024) { // 1MB
    errors.push('分享数据过大，请减少输入内容');
  } else if (estimatedSize > 100 * 1024) { // 100KB
    warnings.push('分享数据较大，可能影响加载速度');
  }

  // 检查版本兼容性
  if (!state.version) {
    warnings.push('缺少版本信息，可能存在兼容性问题');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    canShare: errors.length === 0,
    estimatedSize,
  };
};

// 序列化分享状态
export const serializeShareState = (state: ShareableState): string => {
  try {
    // 添加版本信息
    const stateWithVersion = {
      ...state,
      version: '1.0.0',
      timestamp: Date.now(),
    };

    // 压缩和编码
    const json = JSON.stringify(stateWithVersion);
    return btoa(encodeURIComponent(json));
  } catch (error) {
    throw new ShareError(
      ShareErrorType.INVALID_STATE,
      '无法序列化分享状态',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
};

// 反序列化分享状态
export const deserializeShareState = (encoded: string): ShareableState => {
  try {
    const json = decodeURIComponent(atob(encoded));
    const state = JSON.parse(json) as ShareableState;

    // 验证反序列化的状态
    const validation = validateShareState(state);
    if (!validation.isValid) {
      throw new ShareError(
        ShareErrorType.INVALID_STATE,
        '分享状态无效',
        { errors: validation.errors }
      );
    }

    return state;
  } catch (error) {
    if (error instanceof ShareError) {
      throw error;
    }
    throw new ShareError(
      ShareErrorType.INVALID_STATE,
      '无法解析分享状态',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
};

// 创建分享链接
export const createShareLink = async (
  state: ShareableState,
  options: ShareOptions = {}
): Promise<ShareResult> => {
  try {
    // 验证状态
    const validation = validateShareState(state);
    if (!validation.canShare) {
      return {
        success: false,
        error: validation.errors.join(', '),
      };
    }

    // 生成ID
    const shareId = generateShareId();
    const shortId = generateShortId(DEFAULT_SHARE_CONFIG.shortUrlLength);

    // 计算过期时间
    const expirationDays = options.expirationDays || DEFAULT_SHARE_CONFIG.defaultExpirationDays;
    const expiresAt = new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000);

    // 创建分享链接对象
    const shareLink: ShareLink = {
      id: shareId,
      shortId,
      toolId: state.toolId,
      toolName: state.toolName,
      state,
      createdAt: new Date(),
      expiresAt,
      isPublic: options.isPublic ?? true,
      password: options.password,
      viewCount: 0,
    };

    // 序列化状态用于URL（暂时不使用，为将来扩展保留）
    // const encodedState = serializeShareState(state);

    // 生成URL
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://toollist.app';
    const fullUrl = `${baseUrl}/share/${shortId}`;
    const shortUrl = `${DEFAULT_SHARE_CONFIG.shortUrlDomain}/s/${shortId}`;

    // 在实际应用中，这里应该保存到数据库
    // 现在我们使用localStorage作为临时存储
    if (typeof window !== 'undefined') {
      const existingShares = JSON.parse(localStorage.getItem('shareLinks') || '[]');
      existingShares.push(shareLink);
      localStorage.setItem('shareLinks', JSON.stringify(existingShares));
    }

    return {
      success: true,
      shareId,
      shortUrl,
      fullUrl,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '创建分享链接失败',
    };
  }
};

// 获取分享链接
export const getShareLink = async (shortId: string): Promise<ShareLink | null> => {
  try {
    if (typeof window === 'undefined') {
      return null;
    }

    const existingShares = JSON.parse(localStorage.getItem('shareLinks') || '[]') as ShareLink[];
    const shareLink = existingShares.find(link => link.shortId === shortId);

    if (!shareLink) {
      return null;
    }

    // 检查是否过期
    if (shareLink.expiresAt && new Date() > new Date(shareLink.expiresAt)) {
      throw new ShareError(ShareErrorType.EXPIRED, '分享链接已过期');
    }

    // 更新查看次数
    shareLink.viewCount += 1;
    shareLink.lastViewedAt = new Date();

    // 保存更新
    const updatedShares = existingShares.map(link =>
      link.shortId === shortId ? shareLink : link
    );
    localStorage.setItem('shareLinks', JSON.stringify(updatedShares));

    return shareLink;
  } catch (error) {
    if (error instanceof ShareError) {
      throw error;
    }
    throw new ShareError(
      ShareErrorType.NOT_FOUND,
      '获取分享链接失败',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
};

// 解析分享URL
export const parseShareUrl = (url: string): ParsedShareUrl => {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');

    // 支持多种URL格式
    let shortId = '';
    if (pathParts.includes('share') && pathParts.length > 2) {
      shortId = pathParts[pathParts.indexOf('share') + 1];
    } else if (pathParts.includes('s') && pathParts.length > 2) {
      shortId = pathParts[pathParts.indexOf('s') + 1];
    }

    if (!shortId) {
      return {
        shareId: '',
        isValid: false,
        isExpired: false,
        requiresPassword: false,
        error: ShareErrorType.INVALID_TOOL,
      };
    }

    return {
      shareId: shortId,
      isValid: true,
      isExpired: false,
      requiresPassword: false,
    };
  } catch {
    return {
      shareId: '',
      isValid: false,
      isExpired: false,
      requiresPassword: false,
      error: ShareErrorType.INVALID_TOOL,
    };
  }
};

// 验证分享密码
export const validateSharePassword = (shareLink: ShareLink, password: string): boolean => {
  if (!shareLink.password) {
    return true; // 没有设置密码
  }
  return shareLink.password === password;
};

// 删除分享链接
export const deleteShareLink = async (shortId: string): Promise<boolean> => {
  try {
    if (typeof window === 'undefined') {
      return false;
    }

    const existingShares = JSON.parse(localStorage.getItem('shareLinks') || '[]') as ShareLink[];
    const filteredShares = existingShares.filter(link => link.shortId !== shortId);

    localStorage.setItem('shareLinks', JSON.stringify(filteredShares));
    return true;
  } catch {
    return false;
  }
};

// 获取用户的分享列表
export const getUserShares = async (userId?: string): Promise<ShareLink[]> => {
  try {
    if (typeof window === 'undefined') {
      return [];
    }

    const existingShares = JSON.parse(localStorage.getItem('shareLinks') || '[]') as ShareLink[];

    // 如果没有用户ID，返回所有分享（开发环境）
    if (!userId) {
      return existingShares.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    }

    // 过滤用户的分享
    return existingShares
      .filter(link => link.createdBy === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch {
    return [];
  }
};
