'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import IOPanel from '@/components/tools/IOPanel';
import ShareButton from '@/components/share/ShareButton';

const Base64ConverterClient: React.FC = () => {
  const [input, setInput] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [mode, setMode] = useState<'encode' | 'decode'>('encode');
  const [error, setError] = useState<string>('');

  // 实时转换
  useEffect(() => {
    const timer = setTimeout(() => {
      if (input.trim()) {
        try {
          let result: string;
          if (mode === 'encode') {
            result = btoa(unescape(encodeURIComponent(input)));
          } else {
            result = decodeURIComponent(escape(atob(input)));
          }
          setOutput(result);
          setError('');
        } catch {
          setError(mode === 'encode' ? '编码失败' : '解码失败，请检查输入格式');
          setOutput('');
        }
      } else {
        setOutput('');
        setError('');
      }
    }, 300); // 300ms 防抖

    return () => clearTimeout(timer);
  }, [input, mode]);

  const handleInputChange = (value: string) => {
    setInput(value);
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleExample = () => {
    if (mode === 'encode') {
      setInput('Hello World! 你好世界！');
    } else {
      setInput('SGVsbG8gV29ybGQhIOWNiOWlveS4lueVjO+8gQ==');
    }
  };

  const handleSwap = () => {
    const newMode = mode === 'encode' ? 'decode' : 'encode';
    setMode(newMode);
    setInput(output);
    setOutput('');
  };

  const handleCopyOutput = async () => {
    if (output) {
      try {
        await navigator.clipboard.writeText(output);
        // 这里可以添加成功提示
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const inputActions = [
    { label: '清空', icon: '🗑️', onClick: handleClear },
    { label: '示例', icon: '💡', onClick: handleExample },
    { label: '交换', icon: '🔄', onClick: handleSwap },
  ];

  const outputActions = [
    {
      label: '复制',
      icon: '📋',
      onClick: handleCopyOutput,
      disabled: !output
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Base64编码转换</h1>
          <p className="text-gray-600">
            Base64编码和解码工具，支持中文和特殊字符
          </p>
        </div>

        <div className="space-y-6">
          {/* 模式选择 */}
          <Card>
            <CardHeader>
              <CardTitle>转换模式</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="mode"
                      value="encode"
                      checked={mode === 'encode'}
                      onChange={(e) => setMode(e.target.value as 'encode' | 'decode')}
                      className="mr-2"
                    />
                    <span>编码 (文本 → Base64)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="mode"
                      value="decode"
                      checked={mode === 'decode'}
                      onChange={(e) => setMode(e.target.value as 'encode' | 'decode')}
                      className="mr-2"
                    />
                    <span>解码 (Base64 → 文本)</span>
                  </label>
                </div>

                {/* 分享按钮 */}
                {input && output && !error && (
                  <ShareButton
                    toolId="base64-converter"
                    toolName="Base64 编码解码"
                    input={input}
                    output={output}
                    options={{
                      mode,
                      operation: mode === 'encode' ? '编码' : '解码',
                      inputLength: input.length,
                      outputLength: output.length,
                    }}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* 输入输出面板 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入面板 */}
            <Card className="h-96">
              <IOPanel
                title={mode === 'encode' ? '文本输入' : 'Base64输入'}
                placeholder={mode === 'encode' ? '请输入要编码的文本...' : '请输入要解码的Base64字符串...'}
                value={input}
                onChange={handleInputChange}
                actions={inputActions}
                maxLength={1048576}
                error={error}
              />
            </Card>

            {/* 输出面板 */}
            <Card className="h-96">
              <IOPanel
                title={mode === 'encode' ? 'Base64输出' : '文本输出'}
                placeholder={mode === 'encode' ? 'Base64编码结果将显示在这里...' : '解码后的文本将显示在这里...'}
                value={output}
                readonly
                actions={outputActions}
              />
            </Card>
          </div>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('encode');
                    setInput('Hello World!');
                  }}
                >
                  编码示例
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('decode');
                    setInput('SGVsbG8gV29ybGQh');
                  }}
                >
                  解码示例
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setMode('encode');
                    setInput('你好世界！');
                  }}
                >
                  中文编码
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSwap}
                  disabled={!output}
                >
                  🔄 交换输入输出
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>Base64编码:</strong> 一种基于64个可打印字符来表示二进制数据的表示方法</p>
                <p><strong>主要用途:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>在网络传输中编码二进制数据</li>
                  <li>在URL、Cookie、网页中传输数据</li>
                  <li>在邮件系统中传输附件</li>
                  <li>在JSON中嵌入二进制数据</li>
                </ul>
                <p><strong>特点:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>编码后的数据长度约为原数据的4/3倍</li>
                  <li>只包含A-Z、a-z、0-9、+、/和=字符</li>
                  <li>支持中文和特殊字符</li>
                  <li>可逆转换，无数据丢失</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Base64ConverterClient;
