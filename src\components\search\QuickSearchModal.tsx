'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchStore } from '@/store/searchStore';
import { TOOLS, TOOL_CATEGORIES } from '@/lib/constants/tools';
import { Modal } from '@/components/ui';

interface QuickSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SearchItem {
  id: string;
  title: string;
  description: string;
  url: string;
  type: 'tool' | 'category' | 'page';
  icon?: string;
  category?: string;
}

const QuickSearchModal: React.FC<QuickSearchModalProps> = ({ isOpen, onClose }) => {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { addToHistory } = useSearchStore();

  // 创建搜索项目列表
  const searchItems = useMemo<SearchItem[]>(() => {
    const items: SearchItem[] = [];

    // 添加工具
    TOOLS.forEach(tool => {
      items.push({
        id: tool.id,
        title: tool.name,
        description: tool.description,
        url: tool.path,
        type: 'tool',
        icon: tool.icon,
        category: tool.category,
      });
    });

    // 添加工具分类
    TOOL_CATEGORIES.forEach(category => {
      items.push({
        id: `category-${category.id}`,
        title: category.name,
        description: category.description,
        url: `/tools?category=${category.id}`,
        type: 'category',
        icon: category.icon,
      });
    });

    // 添加页面
    const pages = [
      { id: 'home', title: '首页', description: '返回首页', url: '/', icon: '🏠' },
      { id: 'tools', title: '所有工具', description: '查看所有开发工具', url: '/tools', icon: '🛠️' },
      { id: 'about', title: '关于我们', description: '了解Tool List', url: '/about', icon: 'ℹ️' },
      { id: 'help', title: '帮助中心', description: '获取使用帮助', url: '/help', icon: '❓' },
      { id: 'contact', title: '联系我们', description: '联系方式和反馈', url: '/contact', icon: '📧' },
    ];

    pages.forEach(page => {
      items.push({
        id: page.id,
        title: page.title,
        description: page.description,
        url: page.url,
        type: 'page',
        icon: page.icon,
      });
    });

    return items;
  }, []);

  // 过滤搜索结果
  const filteredItems = useMemo(() => {
    if (!query.trim()) {
      // 没有查询时显示最近使用的工具和常用页面
      return searchItems.filter(item => 
        item.type === 'page' || 
        ['timestamp-converter', 'json-formatter', 'text-converter', 'base64-converter'].includes(item.id)
      ).slice(0, 8);
    }

    const lowerQuery = query.toLowerCase();
    return searchItems.filter(item => 
      item.title.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery) ||
      (item.category && TOOL_CATEGORIES.find(c => c.id === item.category)?.name.toLowerCase().includes(lowerQuery))
    ).slice(0, 10);
  }, [query, searchItems]);

  // 重置状态
  useEffect(() => {
    if (isOpen) {
      setQuery('');
      setSelectedIndex(0);
      // 延迟聚焦，确保模态框已完全打开
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // 更新选中索引
  useEffect(() => {
    setSelectedIndex(0);
  }, [filteredItems]);

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredItems.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredItems.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredItems[selectedIndex]) {
          handleItemSelect(filteredItems[selectedIndex]);
        }
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  // 处理项目选择
  const handleItemSelect = (item: SearchItem) => {
    // 记录到搜索历史
    if (query.trim()) {
      addToHistory(query.trim(), 1);
    }
    
    // 导航到目标页面
    router.push(item.url);
    onClose();
  };

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'tool': return '工具';
      case 'category': return '分类';
      case 'page': return '页面';
      default: return '';
    }
  };

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'tool': return 'bg-blue-100 text-blue-800';
      case 'category': return 'bg-green-100 text-green-800';
      case 'page': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-2xl">
      <div className="p-0">
        {/* 搜索输入框 */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              🔍
            </div>
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="搜索工具、分类或页面..."
              className="w-full pl-10 pr-4 py-3 text-lg border-0 focus:outline-none focus:ring-0"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
              ESC 关闭
            </div>
          </div>
        </div>

        {/* 搜索结果 */}
        <div className="max-h-96 overflow-y-auto">
          {filteredItems.length > 0 ? (
            <div className="p-2">
              {!query.trim() && (
                <div className="px-3 py-2 text-sm text-gray-500 font-medium">
                  快速访问
                </div>
              )}
              {filteredItems.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => handleItemSelect(item)}
                  className={`w-full text-left p-3 rounded-lg flex items-center space-x-3 transition-colors ${
                    index === selectedIndex
                      ? 'bg-primary-50 border border-primary-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="text-2xl">{item.icon}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900 truncate">
                        {item.title}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(item.type)}`}>
                        {getTypeLabel(item.type)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 truncate">
                      {item.description}
                    </div>
                  </div>
                  <div className="text-gray-400">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <div className="text-4xl mb-2">🔍</div>
              <div className="text-lg font-medium mb-1">未找到匹配结果</div>
              <div className="text-sm">尝试使用不同的关键词</div>
            </div>
          )}
        </div>

        {/* 底部提示 */}
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              <span>↑↓ 选择</span>
              <span>↵ 确认</span>
              <span>ESC 关闭</span>
            </div>
            <div>
              Ctrl+K 快速搜索
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default QuickSearchModal;
