import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db/mongodb';
import { Feedback } from '@/lib/db/models';
import { z } from 'zod';

// 反馈提交验证schema
const feedbackSchema = z.object({
  type: z.enum(['bug', 'feature', 'improvement', 'question', 'other']),
  title: z.string().min(1, '标题不能为空').max(200, '标题最多200个字符'),
  content: z.string().min(1, '内容不能为空').max(2000, '内容最多2000个字符'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  toolId: z.string().optional(),
  tags: z.array(z.string()).default([]),
});

// 反馈查询验证schema
const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
  status: z.enum(['pending', 'in-progress', 'resolved', 'closed']).optional(),
  type: z.enum(['bug', 'feature', 'improvement', 'question', 'other']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  search: z.string().optional(),
});

// 提交反馈
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);

    // 要求用户必须登录
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: '请先登录后再提交反馈',
      }, { status: 401 });
    }

    const body = await request.json();

    // 验证输入数据
    const validationResult = feedbackSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        message: '输入数据验证失败',
        errors: validationResult.error.errors,
      }, { status: 400 });
    }

    const { type, title, content, priority, toolId, tags } = validationResult.data;

    // 使用登录用户的信息
    const userId = session.user.id;
    const userEmail = session.user.email;

    // 创建反馈记录
    const feedback = new Feedback({
      type,
      title,
      content,
      priority,
      email: userEmail,
      userId: userId,
      toolId: toolId || null,
      tags,
      status: 'pending',
    });

    await feedback.save();

    // TODO: 发送确认邮件给用户
    // TODO: 发送通知邮件给管理员

    return NextResponse.json({
      success: true,
      message: '反馈提交成功',
      data: {
        id: feedback._id,
        status: feedback.status,
        createdAt: feedback.createdAt,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('提交反馈失败:', error);
    return NextResponse.json({
      success: false,
      message: '提交反馈失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

// 获取反馈列表（管理员）
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);
    
    // 检查管理员权限
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({
        success: false,
        message: '需要管理员权限',
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const queryResult = querySchema.safeParse(Object.fromEntries(searchParams));
    
    if (!queryResult.success) {
      return NextResponse.json({
        success: false,
        message: '查询参数验证失败',
        errors: queryResult.error.errors,
      }, { status: 400 });
    }

    const { page, limit, status, type, priority, search } = queryResult.data;

    // 构建查询条件
    const filter: any = {};
    if (status) filter.status = status;
    if (type) filter.type = type;
    if (priority) filter.priority = priority;
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // 计算分页
    const skip = (page - 1) * limit;

    // 查询反馈列表
    const [feedbacks, total] = await Promise.all([
      Feedback.find(filter)
        .populate('userId', 'username email')
        .populate('resolvedBy', 'username email')
        .sort({ priority: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Feedback.countDocuments(filter),
    ]);

    // 获取统计信息
    const stats = await Feedback.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    const statusStats = stats.reduce((acc, stat) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        feedbacks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats: statusStats,
      },
    });

  } catch (error) {
    console.error('获取反馈列表失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取反馈列表失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
