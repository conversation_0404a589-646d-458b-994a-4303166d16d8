import mongoose, { Document, Schema } from 'mongoose';

// 工具接口
export interface ITool extends Document {
  _id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  path: string;
  isPublic: boolean;
  requiredAuth: boolean;
  visitCount: number;
  lastVisited?: Date;
  addedBy: string;
  config?: {
    maxInputSize?: number;
    allowedFileTypes?: string[];
    rateLimit?: {
      requests: number;
      window: number;
    };
  };
  metadata?: {
    version?: string;
    author?: string;
    license?: string;
    repository?: string;
    documentation?: string;
  };
  status: 'active' | 'inactive' | 'maintenance';
  featured: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

// 工具 Schema
const ToolSchema = new Schema<ITool>({
  name: {
    type: String,
    required: [true, '工具名称是必需的'],
    trim: true,
    minlength: [2, '工具名称至少需要2个字符'],
    maxlength: [100, '工具名称不能超过100个字符'],
  },
  description: {
    type: String,
    required: [true, '工具描述是必需的'],
    trim: true,
    maxlength: [500, '工具描述不能超过500个字符'],
  },
  icon: {
    type: String,
    required: [true, '工具图标是必需的'],
    trim: true,
  },
  category: {
    type: String,
    required: [true, '工具分类是必需的'],
    trim: true,
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签不能超过50个字符'],
  }],
  path: {
    type: String,
    required: [true, '工具路径是必需的'],
    trim: true,
    unique: true,
    match: [/^\/[a-z0-9\-\/]*$/, '路径格式不正确'],
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
  requiredAuth: {
    type: Boolean,
    default: false,
  },
  visitCount: {
    type: Number,
    default: 0,
    min: [0, '访问次数不能为负数'],
  },
  lastVisited: {
    type: Date,
    default: null,
  },
  addedBy: {
    type: String,
    required: [true, '添加者是必需的'],
    default: 'system',
  },
  config: {
    maxInputSize: {
      type: Number,
      min: [0, '最大输入大小不能为负数'],
    },
    allowedFileTypes: [{
      type: String,
      trim: true,
    }],
    rateLimit: {
      requests: {
        type: Number,
        min: [1, '请求次数至少为1'],
      },
      window: {
        type: Number,
        min: [1000, '时间窗口至少为1000毫秒'],
      },
    },
  },
  metadata: {
    version: {
      type: String,
      trim: true,
    },
    author: {
      type: String,
      trim: true,
    },
    license: {
      type: String,
      trim: true,
    },
    repository: {
      type: String,
      trim: true,
    },
    documentation: {
      type: String,
      trim: true,
    },
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance'],
    default: 'active',
  },
  featured: {
    type: Boolean,
    default: false,
  },
  order: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// 索引
ToolSchema.index({ name: 1 });
ToolSchema.index({ category: 1 });
ToolSchema.index({ tags: 1 });
ToolSchema.index({ isPublic: 1 });
ToolSchema.index({ status: 1 });
ToolSchema.index({ featured: 1 });
ToolSchema.index({ visitCount: -1 });
ToolSchema.index({ createdAt: 1 });
ToolSchema.index({ path: 1 }, { unique: true });
ToolSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text'
}, {
  name: 'tool_text_search'
});

// 中间件：保存前处理
ToolSchema.pre('save', function(next) {
  // 确保路径以 / 开头
  if (this.path && !this.path.startsWith('/')) {
    this.path = '/' + this.path;
  }

  // 清理标签
  if (this.tags) {
    this.tags = this.tags
      .filter(tag => tag && tag.trim())
      .map(tag => tag.trim().toLowerCase())
      .filter((tag, index, arr) => arr.indexOf(tag) === index); // 去重
  }

  next();
});

// 实例方法：增加访问次数
ToolSchema.methods.incrementVisit = function() {
  this.visitCount += 1;
  this.lastVisited = new Date();
  return this.save();
};

// 实例方法：检查是否可访问
ToolSchema.methods.isAccessible = function(user?: { role?: string }) {
  // 如果工具不是公开的，需要检查权限
  if (!this.isPublic) {
    return false;
  }

  // 如果工具需要认证，检查用户是否已登录
  if (this.requiredAuth && !user) {
    return false;
  }

  // 如果工具处于维护状态，只有管理员可以访问
  if (this.status === 'maintenance' && (!user || user.role !== 'admin')) {
    return false;
  }

  // 如果工具处于非活跃状态，不可访问
  if (this.status === 'inactive') {
    return false;
  }

  return true;
};

// 静态方法：搜索工具
ToolSchema.statics.search = function(query: string, options: Record<string, unknown> = {}) {
  const {
    category,
    tags,
    isPublic = true,
    status = 'active',
    limit = 20,
    skip = 0,
    sort = { visitCount: -1 }
  } = options;

  const searchQuery: Record<string, unknown> = {
    isPublic,
    status,
  };

  // 文本搜索
  if (query) {
    searchQuery.$text = { $search: query };
  }

  // 分类过滤
  if (category) {
    searchQuery.category = category;
  }

  // 标签过滤
  if (tags && Array.isArray(tags) && tags.length > 0) {
    searchQuery.tags = { $in: tags };
  }

  return this.find(searchQuery)
    .sort(sort)
    .limit(limit)
    .skip(skip);
};

// 静态方法：获取热门工具
ToolSchema.statics.getPopular = function(limit = 10) {
  return this.find({
    isPublic: true,
    status: 'active',
  })
    .sort({ visitCount: -1 })
    .limit(limit);
};

// 静态方法：获取推荐工具
ToolSchema.statics.getFeatured = function(limit = 6) {
  return this.find({
    isPublic: true,
    status: 'active',
    featured: true,
  })
    .sort({ order: 1, createdAt: -1 })
    .limit(limit);
};

// 静态方法：按分类获取工具
ToolSchema.statics.getByCategory = function(category: string, limit = 20) {
  return this.find({
    category,
    isPublic: true,
    status: 'active',
  })
    .sort({ order: 1, visitCount: -1 })
    .limit(limit);
};

// 虚拟字段：工具 URL
ToolSchema.virtual('url').get(function() {
  return `/tools${this.path}`;
});

// 虚拟字段：是否为新工具（7天内创建）
ToolSchema.virtual('isNew').get(function() {
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  return this.createdAt > sevenDaysAgo;
});

// 确保虚拟字段被序列化
ToolSchema.set('toJSON', { virtuals: true });
ToolSchema.set('toObject', { virtuals: true });

// 导出模型
const Tool = mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);

export default Tool;
