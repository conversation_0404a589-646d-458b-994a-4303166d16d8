'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, Card<PERSON>itle, Button, CopyToast } from '@/components/ui';
import ShareButton from '@/components/share/ShareButton';
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard';

interface DateDifference {
  totalSeconds: number;
  totalMinutes: number;
  totalHours: number;
  totalDays: number;
  years: number;
  months: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  humanReadable: string;
  isNegative: boolean;
}

const DateCalculatorClient: React.FC = () => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [difference, setDifference] = useState<DateDifference | null>(null);
  const [error, setError] = useState<string>('');
  const { copyState, copyToClipboard, hideCopyToast } = useCopyToClipboard();

  // 格式化日期为 datetime-local 输入格式
  const formatDateForInput = useCallback((date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }, []);

  // 计算日期差值
  const calculateDifference = useCallback((start: string, end: string) => {
    if (!start.trim() || !end.trim()) {
      setDifference(null);
      setError('');
      return;
    }

    const startDate = new Date(start);
    const endDate = new Date(end);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      setError('无效的日期格式');
      setDifference(null);
      return;
    }

    setError('');

    // 计算时间差（毫秒）
    const diffMs = endDate.getTime() - startDate.getTime();
    const isNegative = diffMs < 0;
    const absDiffMs = Math.abs(diffMs);

    // 计算各种时间单位
    const totalSeconds = Math.floor(absDiffMs / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const totalHours = Math.floor(totalMinutes / 60);
    const totalDays = Math.floor(totalHours / 24);

    // 计算精确的年月日时分秒
    const years = Math.floor(totalDays / 365);
    const remainingDaysAfterYears = totalDays % 365;
    const months = Math.floor(remainingDaysAfterYears / 30);
    const days = remainingDaysAfterYears % 30;
    const hours = totalHours % 24;
    const minutes = totalMinutes % 60;
    const seconds = totalSeconds % 60;

    // 生成可读格式
    const parts: string[] = [];
    if (years > 0) parts.push(`${years}年`);
    if (months > 0) parts.push(`${months}个月`);
    if (days > 0) parts.push(`${days}天`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`);

    const humanReadable = parts.join(' ');

    setDifference({
      totalSeconds: isNegative ? -totalSeconds : totalSeconds,
      totalMinutes: isNegative ? -totalMinutes : totalMinutes,
      totalHours: isNegative ? -totalHours : totalHours,
      totalDays: isNegative ? -totalDays : totalDays,
      years,
      months,
      days,
      hours,
      minutes,
      seconds,
      humanReadable: isNegative ? `负 ${humanReadable}` : humanReadable,
      isNegative
    });
  }, []);

  // 实时计算
  useEffect(() => {
    calculateDifference(startDate, endDate);
  }, [startDate, endDate, calculateDifference]);

  // 初始化默认值
  useEffect(() => {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    setStartDate(formatDateForInput(yesterday));
    setEndDate(formatDateForInput(now));
  }, [formatDateForInput]);

  // 复制功能
  const copyValue = (value: string | number, label: string) => {
    copyToClipboard(String(value), `已复制${label}: ${value}`);
  };

  // 快速设置时间差
  const setQuickDifference = (type: string) => {
    const now = new Date();
    let start: Date;

    switch (type) {
      case '1hour':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '1day':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '1week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1month':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        return;
    }

    setStartDate(formatDateForInput(start));
    setEndDate(formatDateForInput(now));
  };

  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-gray-700">工具</Link>
          <span>/</span>
          <span className="text-gray-900">日期时间计算器</span>
        </nav>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">⏰ 日期时间计算器</h1>
          <div className="flex space-x-2">
            <Link
              href="/tools"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              🏠 返回工具列表
            </Link>
          </div>
        </div>
      </div>

      {/* 主要功能区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="text-2xl">📅</span>
            <span>日期时间差值计算</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-gray-600 text-sm">
            计算两个日期时间之间的精确差值，支持秒时间戳差值和可读格式显示
          </p>

          {/* 快速设置按钮 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              快速设置时间差：
            </label>
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={() => setQuickDifference('1hour')}
                className="bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 border-0 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-3 py-2 rounded-lg"
              >
                1小时前
              </Button>
              <Button
                onClick={() => setQuickDifference('1day')}
                className="bg-emerald-500 text-white hover:bg-emerald-600 active:bg-emerald-700 border-0 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-3 py-2 rounded-lg"
              >
                1天前
              </Button>
              <Button
                onClick={() => setQuickDifference('1week')}
                className="bg-purple-500 text-white hover:bg-purple-600 active:bg-purple-700 border-0 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-3 py-2 rounded-lg"
              >
                1周前
              </Button>
              <Button
                onClick={() => setQuickDifference('1month')}
                className="bg-orange-500 text-white hover:bg-orange-600 active:bg-orange-700 border-0 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-3 py-2 rounded-lg"
              >
                1个月前
              </Button>
              <Button
                onClick={() => setQuickDifference('1year')}
                className="bg-pink-500 text-white hover:bg-pink-600 active:bg-pink-700 border-0 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 px-3 py-2 rounded-lg"
              >
                1年前
              </Button>
            </div>
          </div>

          {/* 日期输入区域 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                开始日期时间：
              </label>
              <input
                type="datetime-local"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                结束日期时间：
              </label>
              <input
                type="datetime-local"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* 计算结果 */}
          {difference && !error && (
            <div>
              <h3 className="text-lg font-semibold mb-4 text-gray-900">计算结果</h3>

              {/* 时间戳差值 */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium text-gray-900 mb-3">时间戳差值（秒）</h4>
                <div className="flex items-center justify-between bg-white rounded border p-3">
                  <span className="font-mono text-lg text-gray-900">
                    {difference.totalSeconds.toLocaleString()}
                  </span>
                  <Button
                    onClick={() => copyValue(difference.totalSeconds, '时间戳差值')}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-xs"
                  >
                    📋 复制
                  </Button>
                </div>
              </div>

              {/* 可读格式 */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium text-gray-900 mb-3">可读格式</h4>
                <div className="flex items-center justify-between bg-white rounded border p-3">
                  <span className="text-lg text-gray-900">
                    {difference.humanReadable}
                  </span>
                  <Button
                    onClick={() => copyValue(difference.humanReadable, '可读格式')}
                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 text-xs"
                  >
                    📋 复制
                  </Button>
                </div>
              </div>

              {/* 详细分解 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">详细分解</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="bg-white rounded border p-3 text-center">
                    <div className="text-2xl font-bold text-blue-600">{Math.abs(difference.totalDays).toLocaleString()}</div>
                    <div className="text-xs text-gray-600">总天数</div>
                    <Button
                      onClick={() => copyValue(Math.abs(difference.totalDays), '总天数')}
                      className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 text-xs font-medium border-0 transition-all duration-200 shadow-sm hover:shadow-md rounded-md"
                    >
                      📋 复制
                    </Button>
                  </div>
                  <div className="bg-white rounded border p-3 text-center">
                    <div className="text-2xl font-bold text-emerald-600">{Math.abs(difference.totalHours).toLocaleString()}</div>
                    <div className="text-xs text-gray-600">总小时数</div>
                    <Button
                      onClick={() => copyValue(Math.abs(difference.totalHours), '总小时数')}
                      className="mt-2 bg-emerald-500 hover:bg-emerald-600 text-white px-3 py-1.5 text-xs font-medium border-0 transition-all duration-200 shadow-sm hover:shadow-md rounded-md"
                    >
                      📋 复制
                    </Button>
                  </div>
                  <div className="bg-white rounded border p-3 text-center">
                    <div className="text-2xl font-bold text-purple-600">{Math.abs(difference.totalMinutes).toLocaleString()}</div>
                    <div className="text-xs text-gray-600">总分钟数</div>
                    <Button
                      onClick={() => copyValue(Math.abs(difference.totalMinutes), '总分钟数')}
                      className="mt-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1.5 text-xs font-medium border-0 transition-all duration-200 shadow-sm hover:shadow-md rounded-md"
                    >
                      📋 复制
                    </Button>
                  </div>
                  <div className="bg-white rounded border p-3 text-center">
                    <div className="text-2xl font-bold text-orange-600">{Math.abs(difference.totalSeconds).toLocaleString()}</div>
                    <div className="text-xs text-gray-600">总秒数</div>
                    <Button
                      onClick={() => copyValue(Math.abs(difference.totalSeconds), '总秒数')}
                      className="mt-2 bg-orange-500 hover:bg-orange-600 text-white px-3 py-1.5 text-xs font-medium border-0 transition-all duration-200 shadow-sm hover:shadow-md rounded-md"
                    >
                      📋 复制
                    </Button>
                  </div>
                </div>
              </div>

              {/* 分享按钮 */}
              {difference && (
                <div className="flex justify-end mt-4">
                  <ShareButton
                    toolId="date-calculator"
                    toolName="日期时间计算器"
                    input={`${startDate} 到 ${endDate}`}
                    output={difference.humanReadable}
                    options={{
                      startDate,
                      endDate,
                      totalSeconds: difference.totalSeconds,
                      totalDays: difference.totalDays,
                      isNegative: difference.isNegative,
                    }}
                    size="sm"
                  />
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3">使用说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">功能特性</h4>
              <ul className="space-y-1">
                <li>• 精确计算两个日期时间之间的差值</li>
                <li>• 支持秒时间戳差值显示</li>
                <li>• 提供可读格式的时间差</li>
                <li>• 显示详细的时间分解</li>
                <li>• 支持负数时间差（开始时间晚于结束时间）</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">使用技巧</h4>
              <ul className="space-y-1">
                <li>• 使用快速设置按钮快速选择常用时间差</li>
                <li>• 点击复制按钮快速复制结果</li>
                <li>• 支持实时计算，修改日期时自动更新结果</li>
                <li>• 可以计算过去和未来的时间差</li>
                <li>• 结果包含总计和详细分解两种格式</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 返回按钮 */}
      <div className="mt-8 text-center">
        <Link
          href="/tools"
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          ← 返回工具列表
        </Link>
      </div>

      {/* 复制成功提示 */}
      <CopyToast
        show={copyState.show}
        message={copyState.message}
        onHide={hideCopyToast}
      />
    </div>
  );
};

export default DateCalculatorClient;
