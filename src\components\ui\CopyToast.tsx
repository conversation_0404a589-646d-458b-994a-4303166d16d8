'use client';

import React, { useEffect, useState } from 'react';

interface CopyToastProps {
  show: boolean;
  message: string;
  onHide?: () => void;
  duration?: number;
  position?: 'top' | 'center' | 'bottom';
}

const CopyToast: React.FC<CopyToastProps> = ({
  show,
  message,
  onHide,
  duration = 3000,
  position = 'top'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => {
          onHide?.();
        }, 500); // 等待动画完成
      }, duration);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [show, duration, onHide]);

  if (!show && !isVisible) return null;

  const positionClasses = {
    top: 'top-20',
    center: 'top-1/2 -translate-y-1/2',
    bottom: 'bottom-20'
  };

  return (
    <>
      <div
        className={`fixed ${positionClasses[position]} left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ease-out max-w-xs`}
        style={{
          animation: isVisible ? 'copySuccess 3s ease-out forwards' : 'none',
          opacity: isVisible ? 1 : 0
        }}
      >
        <div className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
          <span className="text-lg">✓</span>
          <span className="font-mono text-sm break-all">{message}</span>
        </div>
      </div>

      {/* 添加自定义动画样式 */}
      <style jsx>{`
        @keyframes copySuccess {
          0% {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px) scale(0.8);
          }
          20% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1.1);
          }
          40% {
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          80% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          100% {
            opacity: 0;
            transform: translateX(-50%) translateY(-10px) scale(0.9);
          }
        }
      `}</style>
    </>
  );
};

export default CopyToast;
