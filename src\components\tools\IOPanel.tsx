'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { IOPanelProps } from '@/types/tools';
import { cn } from '@/lib/utils';

const IOPanel: React.FC<IOPanelProps> = ({
  title,
  placeholder = '请输入内容...',
  value,
  onChange,
  readonly = false,
  language,
  actions = [],
  maxLength,
  showLineNumbers = false,
  error,
  className,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange && !readonly) {
      const newValue = event.target.value;
      if (!maxLength || newValue.length <= maxLength) {
        onChange(newValue);
      }
    }
  };

  const lineCount = value.split('\n').length;
  const charCount = value.length;

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        
        {/* Actions */}
        {actions.length > 0 && (
          <div className="flex items-center space-x-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={action.onClick}
                disabled={action.disabled}
                className="text-gray-600 hover:text-gray-900"
              >
                <span className="mr-1">{action.icon}</span>
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 relative">
        {/* Line Numbers */}
        {showLineNumbers && (
          <div className="absolute left-0 top-0 bottom-0 w-12 bg-gray-100 border-r border-gray-200 text-xs text-gray-500 font-mono overflow-hidden">
            <div className="p-2">
              {Array.from({ length: lineCount }, (_, i) => (
                <div key={i + 1} className="leading-6 text-right">
                  {i + 1}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Textarea */}
        <textarea
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          readOnly={readonly}
          className={cn(
            'w-full h-full resize-none border-0 focus:ring-0 focus:outline-none p-4 font-mono text-sm leading-6',
            showLineNumbers && 'pl-16',
            readonly && 'bg-gray-50 text-gray-700',
            error && 'bg-red-50 text-red-900',
            language && `language-${language}`
          )}
          style={{ minHeight: '300px' }}
        />

        {/* Error Overlay */}
        {error && (
          <div className="absolute top-2 right-2 bg-red-100 border border-red-300 rounded-md p-2 text-sm text-red-700 max-w-xs">
            <div className="flex items-start">
              <span className="mr-2">⚠️</span>
              <span>{error}</span>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>行数: {lineCount}</span>
          <span>字符: {charCount}</span>
          {maxLength && (
            <span className={charCount > maxLength * 0.9 ? 'text-orange-600' : ''}>
              限制: {maxLength}
            </span>
          )}
        </div>
        
        {language && (
          <div className="flex items-center">
            <span className="mr-1">语言:</span>
            <span className="font-medium">{language.toUpperCase()}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default IOPanel;
