import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

// 用户偏好设置接口
export interface IUserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  favoriteTools: string[];
  recentTools: Array<{
    toolId: string;
    lastUsed: Date;
  }>;
}

// 用户订阅接口
export interface IUserSubscription {
  plan: 'free' | 'pro' | 'enterprise';
  expiresAt: Date;
  features: string[];
}

// 用户文档接口
export interface IUser extends Document {
  email: string;
  username: string;
  password?: string; // 可选，因为可能通过第三方登录
  avatar?: string;
  role: 'user' | 'admin' | 'premium';
  preferences: IUserPreferences;
  subscription?: IUserSubscription;
  lastLoginAt: Date;
  isEmailVerified: boolean;
  twoFactorEnabled: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  providers: Array<{
    provider: string;
    providerId: string;
  }>;
  createdAt: Date;
  updatedAt: Date;

  // 实例方法
  comparePassword(candidatePassword: string): Promise<boolean>;
  addRecentTool(toolId: string): Promise<void>;
  toggleFavoriteTool(toolId: string): Promise<void>;
}

// 用户偏好设置 Schema
const userPreferencesSchema = new Schema<IUserPreferences>({
  theme: {
    type: String,
    enum: ['light', 'dark', 'auto'],
    default: 'auto',
  },
  language: {
    type: String,
    enum: ['zh-CN', 'en-US'],
    default: 'zh-CN',
  },
  favoriteTools: [{
    type: String,
  }],
  recentTools: [{
    toolId: {
      type: String,
      required: true,
    },
    lastUsed: {
      type: Date,
      default: Date.now,
    },
  }],
}, { _id: false });

// 用户订阅 Schema
const userSubscriptionSchema = new Schema<IUserSubscription>({
  plan: {
    type: String,
    enum: ['free', 'pro', 'enterprise'],
    default: 'free',
  },
  expiresAt: {
    type: Date,
    required: true,
  },
  features: [{
    type: String,
  }],
}, { _id: false });

// 用户 Schema
const userSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址'],
  },
  username: {
    type: String,
    required: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [20, '用户名最多20个字符'],
    match: [/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'],
  },
  password: {
    type: String,
    minlength: [8, '密码至少需要8个字符'],
    select: false, // 默认不返回密码字段
  },
  avatar: {
    type: String,
    default: null,
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'premium'],
    default: 'user',
  },
  preferences: {
    type: userPreferencesSchema,
    default: () => ({}),
  },
  subscription: {
    type: userSubscriptionSchema,
    default: null,
  },
  lastLoginAt: {
    type: Date,
    default: Date.now,
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false,
  },
  emailVerificationToken: {
    type: String,
    select: false,
  },
  passwordResetToken: {
    type: String,
    select: false,
  },
  passwordResetExpires: {
    type: Date,
    select: false,
  },
  providers: [{
    provider: {
      type: String,
      required: true,
    },
    providerId: {
      type: String,
      required: true,
    },
  }],
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      delete ret.passwordResetExpires;
      return ret;
    },
  },
});

// 索引
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ username: 1 }, { unique: true });
userSchema.index({ 'providers.provider': 1, 'providers.providerId': 1 });
userSchema.index({ emailVerificationToken: 1 });
userSchema.index({ passwordResetToken: 1 });

// 密码加密中间件
userSchema.pre('save', async function(next) {
  if (!this.isModified('password') || !this.password) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// 实例方法：比较密码
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// 实例方法：添加最近使用的工具
userSchema.methods.addRecentTool = async function(toolId: string): Promise<void> {
  // 移除已存在的记录
  this.preferences.recentTools = this.preferences.recentTools.filter(
    (tool: { toolId: string }) => tool.toolId !== toolId
  );

  // 添加到开头
  this.preferences.recentTools.unshift({
    toolId,
    lastUsed: new Date(),
  });

  // 只保留最近10个
  if (this.preferences.recentTools.length > 10) {
    this.preferences.recentTools = this.preferences.recentTools.slice(0, 10);
  }

  await this.save();
};

// 实例方法：切换收藏工具
userSchema.methods.toggleFavoriteTool = async function(toolId: string): Promise<void> {
  const index = this.preferences.favoriteTools.indexOf(toolId);

  if (index > -1) {
    // 如果已收藏，则取消收藏
    this.preferences.favoriteTools.splice(index, 1);
  } else {
    // 如果未收藏，则添加收藏
    this.preferences.favoriteTools.push(toolId);
  }

  await this.save();
};

// 静态方法接口
interface IUserModel extends mongoose.Model<IUser> {
  findByEmailOrUsername(identifier: string): Promise<IUser | null>;
}

// 静态方法：根据邮箱或用户名查找用户
userSchema.statics.findByEmailOrUsername = function(identifier: string) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { username: identifier },
    ],
  }).select('+password');
};

// 创建模型
const User = (mongoose.models.User || mongoose.model<IUser, IUserModel>('User', userSchema)) as IUserModel;

export default User;
