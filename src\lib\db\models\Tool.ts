import mongoose, { Document, Schema } from 'mongoose';

// 工具文档接口
export interface ITool extends Document {
  name: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  requiredAuth: boolean;
  visitCount: number;
  lastVisited?: Date;
  addedBy: mongoose.Types.ObjectId;
  path: string;
  config?: {
    maxInputSize?: number;
    allowedFileTypes?: string[];
    rateLimit?: {
      requests: number;
      window: number; // 时间窗口（毫秒）
    };
  };
  createdAt: Date;
  updatedAt: Date;

  // 实例方法
  incrementVisitCount(): Promise<void>;
}

// 工具分类文档接口
export interface IToolCategory extends Document {
  name: string;
  icon: string;
  color: string;
  description: string;
  parentId?: mongoose.Types.ObjectId;
  order: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 工具配置 Schema
const toolConfigSchema = new Schema({
  maxInputSize: {
    type: Number,
    default: 1048576, // 1MB
  },
  allowedFileTypes: [{
    type: String,
  }],
  rateLimit: {
    requests: {
      type: Number,
      default: 100,
    },
    window: {
      type: Number,
      default: 900000, // 15分钟
    },
  },
}, { _id: false });

// 工具 Schema
const toolSchema = new Schema<ITool>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: [100, '工具名称最多100个字符'],
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: [500, '工具描述最多500个字符'],
  },
  icon: {
    type: String,
    required: true,
    trim: true,
  },
  category: {
    type: String,
    required: true,
    trim: true,
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签最多50个字符'],
  }],
  isPublic: {
    type: Boolean,
    default: true,
  },
  requiredAuth: {
    type: Boolean,
    default: false,
  },
  visitCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastVisited: {
    type: Date,
    default: null,
  },
  addedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  path: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  config: {
    type: toolConfigSchema,
    default: () => ({}),
  },
}, {
  timestamps: true,
});

// 索引
toolSchema.index({ category: 1, isPublic: 1 });
toolSchema.index({ tags: 1 });
toolSchema.index({ name: 'text', description: 'text' });
toolSchema.index({ visitCount: -1 });
toolSchema.index({ createdAt: -1 });
toolSchema.index({ path: 1 });

// 实例方法：增加访问次数
toolSchema.methods.incrementVisitCount = async function(): Promise<void> {
  this.visitCount += 1;
  this.lastVisited = new Date();
  await this.save();
};

// 工具分类 Schema
const toolCategorySchema = new Schema<IToolCategory>({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: [50, '分类名称最多50个字符'],
  },
  icon: {
    type: String,
    required: true,
    trim: true,
  },
  color: {
    type: String,
    required: true,
    trim: true,
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, '请输入有效的颜色值'],
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, '分类描述最多200个字符'],
  },
  parentId: {
    type: Schema.Types.ObjectId,
    ref: 'ToolCategory',
    default: null,
  },
  order: {
    type: Number,
    default: 0,
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

// 分类索引
toolCategorySchema.index({ parentId: 1, order: 1 });
toolCategorySchema.index({ isPublic: 1, order: 1 });

// 静态方法：获取热门工具
toolSchema.statics.getPopularTools = function(limit = 10) {
  return this.find({ isPublic: true })
    .sort({ visitCount: -1, createdAt: -1 })
    .limit(limit)
    .populate('addedBy', 'username avatar');
};

// 静态方法：按分类获取工具
toolSchema.statics.getToolsByCategory = function(category: string, limit?: number) {
  const query = this.find({ category, isPublic: true })
    .sort({ visitCount: -1, createdAt: -1 })
    .populate('addedBy', 'username avatar');

  if (limit) {
    query.limit(limit);
  }

  return query;
};

// 静态方法：搜索工具
toolSchema.statics.searchTools = function(searchTerm: string, options: {
  category?: string;
  tags?: string[];
  limit?: number;
  skip?: number;
} = {}) {
  const { category, tags, limit = 20, skip = 0 } = options;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const query: any = {
    isPublic: true,
    $text: { $search: searchTerm },
  };

  if (category) {
    query.category = category;
  }

  if (tags && tags.length > 0) {
    query.tags = { $in: tags };
  }

  return this.find(query)
    .sort({ score: { $meta: 'textScore' }, visitCount: -1 })
    .limit(limit)
    .skip(skip)
    .populate('addedBy', 'username avatar');
};

// 创建模型
const Tool = mongoose.models.Tool || mongoose.model<ITool>('Tool', toolSchema);
const ToolCategory = mongoose.models.ToolCategory || mongoose.model<IToolCategory>('ToolCategory', toolCategorySchema);

export { Tool, ToolCategory };
export default Tool;
